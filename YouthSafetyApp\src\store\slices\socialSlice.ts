import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { postsAPI, confessionsAPI } from '@/services/api';
import { Post, Confession } from '@types/index';

interface SocialState {
  posts: Post[];
  confessions: Confession[];
  isLoadingPosts: boolean;
  isLoadingConfessions: boolean;
  isCreatingPost: boolean;
  isCreatingConfession: boolean;
  selectedCategory: string;
  error: string | null;
}

const initialState: SocialState = {
  posts: [],
  confessions: [],
  isLoadingPosts: false,
  isLoadingConfessions: false,
  isCreatingPost: false,
  isCreatingConfession: false,
  selectedCategory: 'all',
  error: null,
};

// Posts async thunks
export const fetchPosts = createAsyncThunk(
  'social/fetchPosts',
  async ({ page = 1, limit = 20, category }: { page?: number; limit?: number; category?: string } = {}, { rejectWithValue }) => {
    try {
      const response = await postsAPI.getFeed(page, limit, category);
      return response.data;
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || 'Failed to fetch posts',
      });
    }
  }
);

export const createPost = createAsyncThunk(
  'social/createPost',
  async (postData: {
    content: string;
    mediaUrls?: string[];
    tags?: string[];
    category?: string;
    privacy?: string;
  }, { rejectWithValue }) => {
    try {
      const response = await postsAPI.create(postData);
      return response.data.post;
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || 'Failed to create post',
      });
    }
  }
);

export const reactToPost = createAsyncThunk(
  'social/reactToPost',
  async ({ postId, reactionType }: { postId: string; reactionType: string }, { rejectWithValue }) => {
    try {
      await postsAPI.react(postId, reactionType as any);
      return { postId, reactionType };
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || 'Failed to react to post',
      });
    }
  }
);

// Confessions async thunks
export const fetchConfessions = createAsyncThunk(
  'social/fetchConfessions',
  async ({ page = 1, limit = 20, category }: { page?: number; limit?: number; category?: string } = {}, { rejectWithValue }) => {
    try {
      const response = await confessionsAPI.getAll(page, limit, category);
      return response.data;
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || 'Failed to fetch confessions',
      });
    }
  }
);

export const createConfession = createAsyncThunk(
  'social/createConfession',
  async (confessionData: {
    content: string;
    isAnonymous?: boolean;
    category?: string;
    supportRequested?: boolean;
  }, { rejectWithValue }) => {
    try {
      const response = await confessionsAPI.create(confessionData);
      return response.data.confession;
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || 'Failed to create confession',
      });
    }
  }
);

const socialSlice = createSlice({
  name: 'social',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSelectedCategory: (state, action: PayloadAction<string>) => {
      state.selectedCategory = action.payload;
    },
    updatePost: (state, action: PayloadAction<Partial<Post> & { id: string }>) => {
      const index = state.posts.findIndex(post => post.id === action.payload.id);
      if (index !== -1) {
        state.posts[index] = { ...state.posts[index], ...action.payload };
      }
    },
    addPost: (state, action: PayloadAction<Post>) => {
      state.posts.unshift(action.payload);
    },
    removePost: (state, action: PayloadAction<string>) => {
      state.posts = state.posts.filter(post => post.id !== action.payload);
    },
  },
  extraReducers: (builder) => {
    // Fetch posts
    builder
      .addCase(fetchPosts.pending, (state) => {
        state.isLoadingPosts = true;
        state.error = null;
      })
      .addCase(fetchPosts.fulfilled, (state, action) => {
        state.isLoadingPosts = false;
        state.posts = action.payload.posts;
      })
      .addCase(fetchPosts.rejected, (state, action) => {
        state.isLoadingPosts = false;
        state.error = action.payload?.message || 'Failed to fetch posts';
      });

    // Create post
    builder
      .addCase(createPost.pending, (state) => {
        state.isCreatingPost = true;
        state.error = null;
      })
      .addCase(createPost.fulfilled, (state, action) => {
        state.isCreatingPost = false;
        state.posts.unshift(action.payload);
      })
      .addCase(createPost.rejected, (state, action) => {
        state.isCreatingPost = false;
        state.error = action.payload?.message || 'Failed to create post';
      });

    // React to post
    builder
      .addCase(reactToPost.fulfilled, (state, action) => {
        const { postId } = action.payload;
        const postIndex = state.posts.findIndex(post => post.id === postId);
        if (postIndex !== -1) {
          // Update reactions would be handled by the component
        }
      });

    // Fetch confessions
    builder
      .addCase(fetchConfessions.pending, (state) => {
        state.isLoadingConfessions = true;
        state.error = null;
      })
      .addCase(fetchConfessions.fulfilled, (state, action) => {
        state.isLoadingConfessions = false;
        state.confessions = action.payload.confessions;
      })
      .addCase(fetchConfessions.rejected, (state, action) => {
        state.isLoadingConfessions = false;
        state.error = action.payload?.message || 'Failed to fetch confessions';
      });

    // Create confession
    builder
      .addCase(createConfession.pending, (state) => {
        state.isCreatingConfession = true;
        state.error = null;
      })
      .addCase(createConfession.fulfilled, (state, action) => {
        state.isCreatingConfession = false;
        state.confessions.unshift(action.payload);
      })
      .addCase(createConfession.rejected, (state, action) => {
        state.isCreatingConfession = false;
        state.error = action.payload?.message || 'Failed to create confession';
      });
  },
});

export const {
  clearError,
  setSelectedCategory,
  updatePost,
  addPost,
  removePost,
} = socialSlice.actions;

export default socialSlice.reducer;
