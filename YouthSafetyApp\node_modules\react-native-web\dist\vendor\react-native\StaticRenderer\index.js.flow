/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow
 */

'use strict';

import * as React from 'react';
type Props = $ReadOnly<{|
  /**
   * Indicates whether the render function needs to be called again
   */
  shouldUpdate: boolean,
  /**
   * () => renderable
   * A function that returns a renderable component
   */
  render: () => React.Node,
|}>;
declare class StaticRenderer extends React.Component<Props> {
  shouldComponentUpdate(nextProps: Props): boolean,
  render(): React.Node,
}
export default StaticRenderer;