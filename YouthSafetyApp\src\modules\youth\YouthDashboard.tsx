import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { YouthUser, SOSRequest } from '@types/index';
import SOSButton from './components/SOSButton';
import QuickActions from './components/QuickActions';
import RecentActivity from './components/RecentActivity';
import SafetyStatus from './components/SafetyStatus';

const { width } = Dimensions.get('window');

interface YouthDashboardProps {
  user: YouthUser;
}

const YouthDashboard: React.FC<YouthDashboardProps> = ({ user }) => {
  const navigation = useNavigation();
  const [activeSOS, setActiveSOS] = useState<SOSRequest | null>(null);
  const [safetyScore, setSafetyScore] = useState(85);

  useEffect(() => {
    // Check for active SOS requests
    checkActiveSOS();
    // Update safety score based on user activity
    updateSafetyScore();
  }, []);

  const checkActiveSOS = async () => {
    try {
      // API call to check for active SOS requests
      // const response = await sosService.getActiveSOS(user.id);
      // setActiveSOS(response.data);
    } catch (error) {
      console.error('Error checking active SOS:', error);
    }
  };

  const updateSafetyScore = () => {
    // Calculate safety score based on various factors
    let score = 100;
    
    // Deduct points for missing emergency contacts
    if (user.emergencyContacts.length === 0) score -= 20;
    if (user.emergencyContacts.length < 2) score -= 10;
    
    // Deduct points for incomplete medical info
    if (!user.medicalInfo.bloodType) score -= 5;
    if (user.medicalInfo.allergies.length === 0) score -= 5;
    
    setSafetyScore(Math.max(score, 0));
  };

  const handleSOSActivation = async (emergencyType: string) => {
    try {
      Alert.alert(
        'Emergency SOS',
        'Are you sure you want to activate emergency SOS?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Activate',
            style: 'destructive',
            onPress: () => activateSOS(emergencyType),
          },
        ]
      );
    } catch (error) {
      console.error('Error activating SOS:', error);
    }
  };

  const activateSOS = async (emergencyType: string) => {
    try {
      // Get current location
      // const location = await LocationService.getCurrentLocation();
      
      // Create SOS request
      const sosRequest: Partial<SOSRequest> = {
        userId: user.id,
        emergencyType: emergencyType as any,
        timestamp: new Date(),
        // location,
        medicalInfo: user.medicalInfo,
        status: 'active',
        priority: 'high',
      };

      // Send SOS request to backend
      // const response = await sosService.createSOS(sosRequest);
      // setActiveSOS(response.data);

      // Navigate to SOS tracking screen
      navigation.navigate('SOSTracking' as never);
    } catch (error) {
      console.error('Error creating SOS request:', error);
      Alert.alert('Error', 'Failed to activate SOS. Please try again.');
    }
  };

  const navigateToConfessions = () => {
    navigation.navigate('ConfessionsRoom' as never);
  };

  const navigateToSocialFeed = () => {
    navigation.navigate('SocialFeed' as never);
  };

  const navigateToProfile = () => {
    navigation.navigate('YouthProfile' as never);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>Hello, {user.firstName}!</Text>
            <Text style={styles.subtitle}>Stay safe and connected</Text>
          </View>
          <TouchableOpacity onPress={navigateToProfile}>
            <Ionicons name="person-circle-outline" size={32} color="#6366f1" />
          </TouchableOpacity>
        </View>

        {/* Safety Status */}
        <SafetyStatus score={safetyScore} user={user} />

        {/* SOS Button */}
        <View style={styles.sosContainer}>
          <SOSButton
            onPress={handleSOSActivation}
            isActive={!!activeSOS}
            activeSOS={activeSOS}
          />
        </View>

        {/* Quick Actions */}
        <QuickActions
          onConfessions={navigateToConfessions}
          onSocialFeed={navigateToSocialFeed}
          onEmergencyContacts={() => navigation.navigate('EmergencyContacts' as never)}
          onSafetyTips={() => navigation.navigate('SafetyTips' as never)}
        />

        {/* Recent Activity */}
        <RecentActivity userId={user.id} />

        {/* Mental Health Check */}
        <View style={styles.mentalHealthCard}>
          <View style={styles.cardHeader}>
            <Ionicons name="heart" size={24} color="#ef4444" />
            <Text style={styles.cardTitle}>Mental Health Check</Text>
          </View>
          <Text style={styles.cardDescription}>
            How are you feeling today? Take a moment to check in with yourself.
          </Text>
          <TouchableOpacity
            style={styles.checkInButton}
            onPress={() => navigation.navigate('MoodTracker' as never)}
          >
            <Text style={styles.checkInButtonText}>Daily Check-in</Text>
          </TouchableOpacity>
        </View>

        {/* Community Events */}
        <View style={styles.eventsCard}>
          <View style={styles.cardHeader}>
            <Ionicons name="calendar" size={24} color="#10b981" />
            <Text style={styles.cardTitle}>Upcoming Events</Text>
          </View>
          <Text style={styles.cardDescription}>
            Join community activities and make new connections.
          </Text>
          <TouchableOpacity
            style={styles.viewEventsButton}
            onPress={() => navigation.navigate('CommunityEvents' as never)}
          >
            <Text style={styles.viewEventsButtonText}>View Events</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    marginTop: 4,
  },
  sosContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  mentalHealthCard: {
    backgroundColor: '#ffffff',
    margin: 16,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  eventsCard: {
    backgroundColor: '#ffffff',
    margin: 16,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 32,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginLeft: 8,
  },
  cardDescription: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
    marginBottom: 16,
  },
  checkInButton: {
    backgroundColor: '#ef4444',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  checkInButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  viewEventsButton: {
    backgroundColor: '#10b981',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  viewEventsButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default YouthDashboard;
