/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 * @format
 */

import invariant from 'fbjs/lib/invariant';
declare export default class ChildListCollection<TList> {
  _cellKeyToChildren: Map<string, Set<TList>>,
  _childrenToCellKey: Map<TList, string>,
  add(list: TList, cellKey: string): void,
  remove(list: TList): void,
  forEach(fn: (TList) => void): void,
  forEachInCell(cellKey: string, fn: (TList) => void): void,
  anyInCell(cellKey: string, fn: (TList) => boolean): boolean,
  size(): number,
}