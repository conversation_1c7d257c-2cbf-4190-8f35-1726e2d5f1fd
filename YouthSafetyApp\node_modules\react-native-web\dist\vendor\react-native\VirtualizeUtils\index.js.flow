/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

'use strict';

import type { FrameMetricProps } from '../VirtualizedList/VirtualizedListProps';

/**
 * Used to find the indices of the frames that overlap the given offsets. Useful for finding the
 * items that bound different windows of content, such as the visible area or the buffered overscan
 * area.
 */
declare export function elementsThatOverlapOffsets(offsets: Array<number>, props: FrameMetricProps, getFrameMetrics: (index: number, props: FrameMetricProps) => {
  length: number,
  offset: number,
  ...
}, zoomScale: number): Array<number>;
/**
 * Computes the number of elements in the `next` range that are new compared to the `prev` range.
 * Handy for calculating how many new items will be rendered when the render window changes so we
 * can restrict the number of new items render at once so that content can appear on the screen
 * faster.
 */
declare export function newRangeCount(prev: {
  first: number,
  last: number,
  ...
}, next: {
  first: number,
  last: number,
  ...
}): number;
/**
 * Custom logic for determining which items should be rendered given the current frame and scroll
 * metrics, as well as the previous render state. The algorithm may evolve over time, but generally
 * prioritizes the visible area first, then expands that with overscan regions ahead and behind,
 * biased in the direction of scroll.
 */
declare export function computeWindowedRenderLimits(props: FrameMetricProps, maxToRenderPerBatch: number, windowSize: number, prev: {
  first: number,
  last: number,
}, getFrameMetricsApprox: (index: number, props: FrameMetricProps) => {
  length: number,
  offset: number,
  ...
}, scrollMetrics: {
  dt: number,
  offset: number,
  velocity: number,
  visibleLength: number,
  zoomScale: number,
  ...
}): {
  first: number,
  last: number,
};
declare export function keyExtractor(item: any, index: number): string;