{"expo": {"name": "Youth Safety App", "slug": "youth-safety-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.youthsafety.app", "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app needs location access for emergency SOS functionality and safety features.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app needs location access for emergency SOS functionality and safety features.", "NSCameraUsageDescription": "This app needs camera access for profile photos and emergency documentation.", "NSMicrophoneUsageDescription": "This app needs microphone access for emergency voice recordings and video calls.", "NSContactsUsageDescription": "This app needs contacts access to set up emergency contacts.", "NSFaceIDUsageDescription": "This app uses Face ID for secure authentication."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.youthsafety.app", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "RECORD_AUDIO", "READ_CONTACTS", "WRITE_CONTACTS", "USE_FINGERPRINT", "USE_BIOMETRIC", "VIBRATE", "RECEIVE_BOOT_COMPLETED", "WAKE_LOCK"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": []}}