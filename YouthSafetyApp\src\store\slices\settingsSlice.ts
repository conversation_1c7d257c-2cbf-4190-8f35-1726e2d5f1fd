import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { 
  NotificationSettings, 
  PrivacySettings, 
  AccessibilitySettings,
  ThemeColors 
} from '@types/index';

interface SettingsState {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  accessibility: AccessibilitySettings;
  isLoading: boolean;
  error: string | null;
  lastSyncedAt: string | null;
}

const initialState: SettingsState = {
  theme: 'auto',
  language: 'en',
  notifications: {
    sosAlerts: true,
    socialUpdates: true,
    eventReminders: true,
    emergencyContacts: true,
    pushEnabled: true,
    emailEnabled: true,
    smsEnabled: false,
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '07:00',
    },
  },
  privacy: {
    profileVisibility: 'friends',
    locationSharing: 'emergency_only',
    dataCollection: false,
    analyticsOptOut: true,
    marketingOptOut: true,
    thirdPartySharing: false,
    anonymousPosting: true,
    emergencyContactsVisible: false,
    medicalInfoVisible: false,
  },
  accessibility: {
    fontSize: 'medium',
    highContrast: false,
    screenReader: false,
    voiceCommands: false,
    hapticFeedback: true,
  },
  isLoading: false,
  error: null,
  lastSyncedAt: null,
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    // Theme settings
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'auto'>) => {
      state.theme = action.payload;
    },
    
    setLanguage: (state, action: PayloadAction<string>) => {
      state.language = action.payload;
    },

    // Notification settings
    updateNotificationSettings: (state, action: PayloadAction<Partial<NotificationSettings>>) => {
      state.notifications = { ...state.notifications, ...action.payload };
    },

    toggleNotification: (state, action: PayloadAction<keyof NotificationSettings>) => {
      const key = action.payload;
      if (typeof state.notifications[key] === 'boolean') {
        (state.notifications[key] as boolean) = !(state.notifications[key] as boolean);
      }
    },

    setQuietHours: (state, action: PayloadAction<{ enabled: boolean; start: string; end: string }>) => {
      state.notifications.quietHours = action.payload;
    },

    // Privacy settings
    updatePrivacySettings: (state, action: PayloadAction<Partial<PrivacySettings>>) => {
      state.privacy = { ...state.privacy, ...action.payload };
    },

    setProfileVisibility: (state, action: PayloadAction<'public' | 'friends' | 'private'>) => {
      state.privacy.profileVisibility = action.payload;
    },

    setLocationSharing: (state, action: PayloadAction<'always' | 'emergency_only' | 'never'>) => {
      state.privacy.locationSharing = action.payload;
    },

    togglePrivacySetting: (state, action: PayloadAction<keyof PrivacySettings>) => {
      const key = action.payload;
      if (typeof state.privacy[key] === 'boolean') {
        (state.privacy[key] as boolean) = !(state.privacy[key] as boolean);
      }
    },

    // Accessibility settings
    updateAccessibilitySettings: (state, action: PayloadAction<Partial<AccessibilitySettings>>) => {
      state.accessibility = { ...state.accessibility, ...action.payload };
    },

    setFontSize: (state, action: PayloadAction<'small' | 'medium' | 'large' | 'extra_large'>) => {
      state.accessibility.fontSize = action.payload;
    },

    toggleAccessibilitySetting: (state, action: PayloadAction<keyof AccessibilitySettings>) => {
      const key = action.payload;
      if (typeof state.accessibility[key] === 'boolean') {
        (state.accessibility[key] as boolean) = !(state.accessibility[key] as boolean);
      }
    },

    // Loading and error states
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.isLoading = false;
    },

    clearError: (state) => {
      state.error = null;
    },

    // Sync
    setSyncedAt: (state, action: PayloadAction<string>) => {
      state.lastSyncedAt = action.payload;
    },

    // Reset settings
    resetToDefaults: (state) => {
      return { ...initialState, lastSyncedAt: state.lastSyncedAt };
    },

    resetNotificationSettings: (state) => {
      state.notifications = initialState.notifications;
    },

    resetPrivacySettings: (state) => {
      state.privacy = initialState.privacy;
    },

    resetAccessibilitySettings: (state) => {
      state.accessibility = initialState.accessibility;
    },

    // Bulk update
    updateAllSettings: (state, action: PayloadAction<Partial<SettingsState>>) => {
      return { ...state, ...action.payload };
    },
  },
});

export const {
  setTheme,
  setLanguage,
  updateNotificationSettings,
  toggleNotification,
  setQuietHours,
  updatePrivacySettings,
  setProfileVisibility,
  setLocationSharing,
  togglePrivacySetting,
  updateAccessibilitySettings,
  setFontSize,
  toggleAccessibilitySetting,
  setLoading,
  setError,
  clearError,
  setSyncedAt,
  resetToDefaults,
  resetNotificationSettings,
  resetPrivacySettings,
  resetAccessibilitySettings,
  updateAllSettings,
} = settingsSlice.actions;

// Selectors
export const selectTheme = (state: { settings: SettingsState }) => state.settings.theme;
export const selectLanguage = (state: { settings: SettingsState }) => state.settings.language;
export const selectNotificationSettings = (state: { settings: SettingsState }) => state.settings.notifications;
export const selectPrivacySettings = (state: { settings: SettingsState }) => state.settings.privacy;
export const selectAccessibilitySettings = (state: { settings: SettingsState }) => state.settings.accessibility;
export const selectSettingsLoading = (state: { settings: SettingsState }) => state.settings.isLoading;
export const selectSettingsError = (state: { settings: SettingsState }) => state.settings.error;
export const selectLastSyncedAt = (state: { settings: SettingsState }) => state.settings.lastSyncedAt;

// Complex selectors
export const selectIsQuietHoursActive = (state: { settings: SettingsState }) => {
  const { quietHours } = state.settings.notifications;
  if (!quietHours.enabled) return false;

  const now = new Date();
  const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
  
  return currentTime >= quietHours.start || currentTime <= quietHours.end;
};

export const selectShouldShowNotification = (state: { settings: SettingsState }, notificationType: keyof NotificationSettings) => {
  const settings = state.settings.notifications;
  const isQuietHours = selectIsQuietHoursActive(state);
  
  if (isQuietHours && notificationType !== 'sosAlerts') {
    return false;
  }
  
  return settings[notificationType] as boolean;
};

export const selectCanShareLocation = (state: { settings: SettingsState }, context: 'emergency' | 'social' | 'analytics') => {
  const { locationSharing } = state.settings.privacy;
  
  switch (context) {
    case 'emergency':
      return locationSharing !== 'never';
    case 'social':
      return locationSharing === 'always';
    case 'analytics':
      return locationSharing === 'always' && !state.settings.privacy.analyticsOptOut;
    default:
      return false;
  }
};

export default settingsSlice.reducer;
