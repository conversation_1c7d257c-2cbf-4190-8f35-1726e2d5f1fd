{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "react-jsx", "lib": ["dom", "esnext"], "moduleResolution": "node", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@youth/*": ["src/modules/youth/*"], "@activist/*": ["src/modules/activist/*"], "@admin/*": ["src/modules/admin/*"], "@shared/*": ["src/shared/*"], "@types/*": ["src/types/*"], "@utils/*": ["src/utils/*"], "@services/*": ["src/services/*"], "@components/*": ["src/components/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules"]}