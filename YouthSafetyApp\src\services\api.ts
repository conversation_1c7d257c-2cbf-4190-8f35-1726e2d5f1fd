// Re-export from the new API service location
export * from './api/index';

// For backward compatibility, also export the old names
import {
  authAPI as newAuthAPI,
  sosAPI as newSosAPI,
  postsAPI as newPostsAPI,
  confessionsAPI as newConfessionsAPI,
  usersAPI as newUsersAPI,
  adminAPI as newAdminAPI,
  apiService
} from './api/index';

export const apiClient = {
  auth: newAuthAPI,
  sos: newSosAPI,
  posts: newPostsAPI,
  confessions: newConfessionsAPI,
  users: newUsersAPI,
  admin: newAdminAPI,
};

export const authAPI = newAuthAPI;
export const sosAPI = newSosAPI;
export const postsAPI = newPostsAPI;
export const confessionsAPI = newConfessionsAPI;
export const usersAPI = newUsersAPI;
export const adminAPI = newAdminAPI;
