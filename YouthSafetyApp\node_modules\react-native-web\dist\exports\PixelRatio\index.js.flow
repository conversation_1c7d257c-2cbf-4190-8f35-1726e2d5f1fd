/**
 * Copyright (c) <PERSON>.
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

import Dimensions from '../Dimensions';

/**
 * PixelRatio gives access to the device pixel density.
 */
declare export default class PixelRatio {
  static get(): number,
  static getFontScale(): number,
  static getPixelSizeForLayoutSize(layoutSize: number): number,
  static roundToNearestPixel(layoutSize: number): number,
}