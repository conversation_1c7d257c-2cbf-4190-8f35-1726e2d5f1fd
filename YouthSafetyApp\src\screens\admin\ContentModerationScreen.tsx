import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

const ContentModerationScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Content Moderation</Text>
        <View style={styles.placeholder}>
          <Ionicons name="flag-outline" size={64} color="#ccc" />
          <Text style={styles.placeholderText}>Moderation Coming Soon</Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  content: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 },
  title: { fontSize: 24, fontWeight: 'bold', color: '#333', marginBottom: 30 },
  placeholder: { alignItems: 'center' },
  placeholderText: { fontSize: 20, fontWeight: 'bold', color: '#666', marginTop: 20 },
});

export default ContentModerationScreen;
