import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import { RootState } from '../store';

// Import screens
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import YouthDashboardScreen from '../screens/youth/YouthDashboardScreen';
import ActivistDashboardScreen from '../screens/activist/ActivistDashboardScreen';
import AdminDashboardScreen from '../screens/admin/AdminDashboardScreen';

export type RootStackParamList = {
  Login: undefined;
  Register: undefined;
  YouthDashboard: undefined;
  ActivistDashboard: undefined;
  AdminDashboard: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  const { isAuthenticated, user } = useSelector((state: RootState) => state.auth);

  if (!isAuthenticated) {
    return (
      <Stack.Navigator
        initialRouteName="Login"
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen name="Login" component={LoginScreen} />
        <Stack.Screen name="Register" component={RegisterScreen} />
      </Stack.Navigator>
    );
  }

  // Authenticated user navigation based on role
  const getDashboardScreen = () => {
    switch (user?.role) {
      case 'activist':
        return ActivistDashboardScreen;
      case 'admin':
        return AdminDashboardScreen;
      case 'youth':
      default:
        return YouthDashboardScreen;
    }
  };

  const getDashboardRoute = () => {
    switch (user?.role) {
      case 'activist':
        return 'ActivistDashboard';
      case 'admin':
        return 'AdminDashboard';
      case 'youth':
      default:
        return 'YouthDashboard';
    }
  };

  return (
    <Stack.Navigator
      initialRouteName={getDashboardRoute() as keyof RootStackParamList}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen
        name="YouthDashboard"
        component={YouthDashboardScreen}
        options={{ title: 'Youth Dashboard' }}
      />
      <Stack.Screen
        name="ActivistDashboard"
        component={ActivistDashboardScreen}
        options={{ title: 'Activist Dashboard' }}
      />
      <Stack.Screen
        name="AdminDashboard"
        component={AdminDashboardScreen}
        options={{ title: 'Admin Dashboard' }}
      />
    </Stack.Navigator>
  );
};

export default AppNavigator;