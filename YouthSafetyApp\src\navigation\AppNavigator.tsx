import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useAppSelector } from '@store/index';
import { selectIsAuthenticated, selectUser } from '@store/slices/authSlice';
import { RootStackParamList } from '@types/index';

// Import screens
import AuthNavigator from './AuthNavigator';
import YouthNavigator from './YouthNavigator';
import ActivistNavigator from './ActivistNavigator';
import AdminNavigator from './AdminNavigator';
import LoadingScreen from '@screens/LoadingScreen';

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const user = useAppSelector(selectUser);
  const isLoading = useAppSelector(state => state.auth.isLoading);

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!isAuthenticated || !user) {
    return <AuthNavigator />;
  }

  // Route to appropriate navigator based on user role
  switch (user.role) {
    case 'youth':
      return <YouthNavigator />;
    case 'activist':
      return <ActivistNavigator />;
    case 'admin':
      return <AdminNavigator />;
    default:
      return <AuthNavigator />;
  }
};

export default AppNavigator;
