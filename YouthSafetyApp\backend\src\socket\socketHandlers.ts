import { Server, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { db } from '../database/connection';
import { logger } from '../utils/logger';
import { findNearestActivists } from '../services/location';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userRole?: string;
}

export const setupSocketHandlers = (io: Server) => {
  // Authentication middleware
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication error: No token provided'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      
      // Get user from database
      const user = await db('users')
        .select('id', 'role', 'is_active')
        .where({ id: decoded.userId })
        .first();

      if (!user || !user.is_active) {
        return next(new Error('Authentication error: Invalid user'));
      }

      socket.userId = user.id;
      socket.userRole = user.role;
      
      next();
    } catch (error) {
      logger.error('Socket authentication error:', error);
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', (socket: AuthenticatedSocket) => {
    logger.info(`User ${socket.userId} connected to socket`);

    // Join user-specific room
    socket.on('join_user_room', (userId: string) => {
      if (socket.userId === userId) {
        socket.join(`user_${userId}`);
        logger.info(`User ${userId} joined their room`);
      }
    });

    // SOS-related handlers
    setupSOSHandlers(socket, io);
    
    // Messaging handlers
    setupMessagingHandlers(socket, io);
    
    // WebRTC handlers
    setupWebRTCHandlers(socket, io);
    
    // Location sharing handlers
    setupLocationHandlers(socket, io);

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info(`User ${socket.userId} disconnected: ${reason}`);
      
      // Update user status to offline
      if (socket.userId) {
        updateUserStatus(socket.userId, 'offline');
      }
    });
  });
};

const setupSOSHandlers = (socket: AuthenticatedSocket, io: Server) => {
  // Join SOS-specific room
  socket.on('join_sos_room', (sosId: string) => {
    socket.join(`sos_${sosId}`);
    logger.info(`User ${socket.userId} joined SOS room ${sosId}`);
  });

  // Leave SOS room
  socket.on('leave_sos_room', (sosId: string) => {
    socket.leave(`sos_${sosId}`);
    logger.info(`User ${socket.userId} left SOS room ${sosId}`);
  });

  // Real-time location updates during SOS
  socket.on('update_sos_location', async (data: { sosId: string; location: { latitude: number; longitude: number } }) => {
    try {
      const { sosId, location } = data;
      
      // Verify user owns this SOS or is assigned activist
      const sosRequest = await db('sos_requests')
        .where({ id: sosId })
        .andWhere(function() {
          this.where({ user_id: socket.userId })
              .orWhere({ assigned_activist_id: socket.userId });
        })
        .first();

      if (!sosRequest) {
        return;
      }

      // Update location in database
      await db('sos_requests')
        .where({ id: sosId })
        .update({
          latitude: location.latitude,
          longitude: location.longitude,
          updated_at: new Date()
        });

      // Broadcast location update to SOS room
      socket.to(`sos_${sosId}`).emit('location_update', {
        sosId,
        location,
        timestamp: new Date(),
        userId: socket.userId
      });

    } catch (error) {
      logger.error('Error updating SOS location:', error);
    }
  });
};

const setupMessagingHandlers = (socket: AuthenticatedSocket, io: Server) => {
  // Send message
  socket.on('send_message', async (data: {
    recipientId: string;
    message: string;
    messageType: 'text' | 'audio' | 'image';
    timestamp: Date;
  }) => {
    try {
      const { recipientId, message, messageType, timestamp } = data;

      // Store message in database
      const [messageRecord] = await db('messages')
        .insert({
          sender_id: socket.userId,
          recipient_id: recipientId,
          content: message,
          message_type: messageType,
          timestamp
        })
        .returning('*');

      // Send to recipient
      io.to(`user_${recipientId}`).emit('new_message', {
        id: messageRecord.id,
        senderId: socket.userId,
        content: message,
        messageType,
        timestamp
      });

    } catch (error) {
      logger.error('Error sending message:', error);
    }
  });
};

const setupWebRTCHandlers = (socket: AuthenticatedSocket, io: Server) => {
  // Initiate call
  socket.on('initiate_call', async (data: {
    recipientId: string;
    callType: 'audio' | 'video';
    timestamp: Date;
  }) => {
    try {
      const { recipientId, callType, timestamp } = data;

      // Create call record
      const [callRecord] = await db('calls')
        .insert({
          caller_id: socket.userId,
          recipient_id: recipientId,
          call_type: callType,
          status: 'ringing',
          started_at: timestamp
        })
        .returning('*');

      // Notify recipient
      io.to(`user_${recipientId}`).emit('incoming_call', {
        callId: callRecord.id,
        callerId: socket.userId,
        callType,
        timestamp
      });

    } catch (error) {
      logger.error('Error initiating call:', error);
    }
  });

  // Accept call
  socket.on('accept_call', async (data: { callId: string }) => {
    try {
      const { callId } = data;

      // Update call status
      const [callRecord] = await db('calls')
        .where({ id: callId })
        .update({ status: 'accepted', accepted_at: new Date() })
        .returning('*');

      if (callRecord) {
        // Notify caller
        io.to(`user_${callRecord.caller_id}`).emit('call_accepted', {
          callId,
          acceptedBy: socket.userId
        });
      }

    } catch (error) {
      logger.error('Error accepting call:', error);
    }
  });

  // Reject call
  socket.on('reject_call', async (data: { callId: string }) => {
    try {
      const { callId } = data;

      // Update call status
      const [callRecord] = await db('calls')
        .where({ id: callId })
        .update({ status: 'rejected', ended_at: new Date() })
        .returning('*');

      if (callRecord) {
        // Notify caller
        io.to(`user_${callRecord.caller_id}`).emit('call_rejected', {
          callId,
          rejectedBy: socket.userId
        });
      }

    } catch (error) {
      logger.error('Error rejecting call:', error);
    }
  });

  // End call
  socket.on('end_call', async (data: { callId: string }) => {
    try {
      const { callId } = data;

      // Update call status
      const [callRecord] = await db('calls')
        .where({ id: callId })
        .update({ status: 'ended', ended_at: new Date() })
        .returning('*');

      if (callRecord) {
        // Notify other participant
        const otherUserId = callRecord.caller_id === socket.userId 
          ? callRecord.recipient_id 
          : callRecord.caller_id;

        io.to(`user_${otherUserId}`).emit('call_ended', {
          callId,
          endedBy: socket.userId
        });
      }

    } catch (error) {
      logger.error('Error ending call:', error);
    }
  });

  // WebRTC signaling
  socket.on('webrtc_offer', (data: { recipientId: string; offer: RTCSessionDescriptionInit }) => {
    io.to(`user_${data.recipientId}`).emit('webrtc_offer', {
      senderId: socket.userId,
      offer: data.offer
    });
  });

  socket.on('webrtc_answer', (data: { recipientId: string; answer: RTCSessionDescriptionInit }) => {
    io.to(`user_${data.recipientId}`).emit('webrtc_answer', {
      senderId: socket.userId,
      answer: data.answer
    });
  });

  socket.on('webrtc_ice_candidate', (data: { recipientId: string; candidate: RTCIceCandidateInit }) => {
    io.to(`user_${data.recipientId}`).emit('webrtc_ice_candidate', {
      senderId: socket.userId,
      candidate: data.candidate
    });
  });
};

const setupLocationHandlers = (socket: AuthenticatedSocket, io: Server) => {
  // Update user status
  socket.on('update_status', async (data: { status: 'online' | 'offline' | 'busy' }) => {
    try {
      await updateUserStatus(socket.userId!, data.status);
    } catch (error) {
      logger.error('Error updating user status:', error);
    }
  });

  // Share location (for activists)
  socket.on('share_location', async (data: { location: { latitude: number; longitude: number }; timestamp: Date }) => {
    try {
      if (socket.userRole !== 'activist') {
        return;
      }

      // Update activist location
      await db('activist_profiles')
        .where({ user_id: socket.userId })
        .update({
          last_location: JSON.stringify(data.location),
          last_online_at: data.timestamp
        });

    } catch (error) {
      logger.error('Error sharing location:', error);
    }
  });
};

const updateUserStatus = async (userId: string, status: string) => {
  try {
    await db('users')
      .where({ id: userId })
      .update({ last_login_at: new Date() });

    // Update role-specific status
    const user = await db('users').where({ id: userId }).first();
    
    if (user?.role === 'activist') {
      await db('activist_profiles')
        .where({ user_id: userId })
        .update({ 
          is_online: status === 'online',
          last_online_at: new Date()
        });
    }
  } catch (error) {
    logger.error('Error updating user status:', error);
  }
};
