import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
  Switch,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAppSelector, useAppDispatch } from '@/store';
import { confessionsAPI } from '@/services/api';
import { Confession } from '@types/index';

interface ConfessionItem extends Confession {
  responses: Array<{
    id: string;
    content: string;
    isFromCounselor: boolean;
    isAnonymous: boolean;
    createdAt: Date;
  }>;
}

const ConfessionsRoomScreen: React.FC = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);

  const [confessions, setConfessions] = useState<ConfessionItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newConfession, setNewConfession] = useState('');
  const [isAnonymous, setIsAnonymous] = useState(true);
  const [supportRequested, setSupportRequested] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('general');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const categories = [
    { id: 'general', name: 'General', icon: 'chatbubble-outline' },
    { id: 'mental_health', name: 'Mental Health', icon: 'heart-outline' },
    { id: 'relationships', name: 'Relationships', icon: 'people-outline' },
    { id: 'family', name: 'Family', icon: 'home-outline' },
    { id: 'school', name: 'School/Work', icon: 'school-outline' },
    { id: 'identity', name: 'Identity', icon: 'person-outline' },
  ];

  useEffect(() => {
    loadConfessions();
  }, []);

  const loadConfessions = async () => {
    try {
      setIsLoading(true);
      const response = await confessionsAPI.getAll(1, 20);
      setConfessions(response.data.confessions);
    } catch (error) {
      console.error('Error loading confessions:', error);
      Alert.alert('Error', 'Failed to load confessions');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateConfession = async () => {
    if (!newConfession.trim()) {
      Alert.alert('Error', 'Please write your confession');
      return;
    }

    if (newConfession.length < 10) {
      Alert.alert('Error', 'Confession must be at least 10 characters long');
      return;
    }

    setIsSubmitting(true);

    try {
      const confessionData = {
        content: newConfession.trim(),
        isAnonymous,
        category: selectedCategory,
        supportRequested,
      };

      const response = await confessionsAPI.create(confessionData);
      
      // Add new confession to the top of the list
      setConfessions(prev => [response.data.confession, ...prev]);
      
      // Reset form
      setNewConfession('');
      setIsAnonymous(true);
      setSupportRequested(false);
      setSelectedCategory('general');
      setShowCreateModal(false);

      Alert.alert('Success', 'Your confession has been shared anonymously');
    } catch (error) {
      console.error('Error creating confession:', error);
      Alert.alert('Error', 'Failed to share confession');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRespondToConfession = async (confessionId: string, response: string) => {
    try {
      await confessionsAPI.respond(confessionId, response, true);
      
      // Reload confessions to show new response
      loadConfessions();
    } catch (error) {
      console.error('Error responding to confession:', error);
      Alert.alert('Error', 'Failed to send response');
    }
  };

  const handleReportConfession = async (confessionId: string) => {
    Alert.alert(
      'Report Confession',
      'Why are you reporting this confession?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Inappropriate Content', onPress: () => reportConfession(confessionId, 'inappropriate') },
        { text: 'Spam', onPress: () => reportConfession(confessionId, 'spam') },
        { text: 'Harmful Content', onPress: () => reportConfession(confessionId, 'harmful') },
      ]
    );
  };

  const reportConfession = async (confessionId: string, reason: string) => {
    try {
      await confessionsAPI.report(confessionId, reason);
      Alert.alert('Thank you', 'Your report has been submitted for review');
    } catch (error) {
      console.error('Error reporting confession:', error);
      Alert.alert('Error', 'Failed to submit report');
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - new Date(date).getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const getCategoryIcon = (category: string) => {
    const cat = categories.find(c => c.id === category);
    return cat?.icon || 'chatbubble-outline';
  };

  const renderConfession = ({ item }: { item: ConfessionItem }) => (
    <View style={styles.confessionCard}>
      <View style={styles.confessionHeader}>
        <View style={styles.categoryContainer}>
          <Ionicons name={getCategoryIcon(item.category)} size={16} color="#6366f1" />
          <Text style={styles.categoryText}>
            {categories.find(c => c.id === item.category)?.name || 'General'}
          </Text>
        </View>
        <View style={styles.confessionMeta}>
          <Text style={styles.timeText}>{formatTimeAgo(item.createdAt)}</Text>
          <TouchableOpacity onPress={() => handleReportConfession(item.id)}>
            <Ionicons name="flag-outline" size={16} color="#6b7280" />
          </TouchableOpacity>
        </View>
      </View>

      <Text style={styles.confessionContent}>{item.content}</Text>

      {item.supportRequested && (
        <View style={styles.supportBadge}>
          <Ionicons name="heart" size={14} color="#ef4444" />
          <Text style={styles.supportText}>Support Requested</Text>
        </View>
      )}

      <View style={styles.confessionActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => {
            // Show response modal
            Alert.prompt(
              'Send Support',
              'Share words of encouragement (anonymous)',
              (text) => {
                if (text && text.trim()) {
                  handleRespondToConfession(item.id, text.trim());
                }
              },
              'plain-text',
              '',
              'default'
            );
          }}
        >
          <Ionicons name="heart-outline" size={18} color="#6366f1" />
          <Text style={styles.actionText}>Support</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="chatbubble-outline" size={18} color="#6366f1" />
          <Text style={styles.actionText}>
            {item.responses?.length || 0} responses
          </Text>
        </TouchableOpacity>
      </View>

      {item.responses && item.responses.length > 0 && (
        <View style={styles.responsesContainer}>
          {item.responses.slice(0, 2).map((response) => (
            <View key={response.id} style={styles.responseItem}>
              <Text style={styles.responseContent}>{response.content}</Text>
              <Text style={styles.responseTime}>
                {formatTimeAgo(response.createdAt)}
                {response.isFromCounselor && (
                  <Text style={styles.counselorBadge}> • Counselor</Text>
                )}
              </Text>
            </View>
          ))}
          {item.responses.length > 2 && (
            <TouchableOpacity style={styles.viewMoreButton}>
              <Text style={styles.viewMoreText}>
                View {item.responses.length - 2} more responses
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="#1f2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Confessions Room</Text>
        <TouchableOpacity onPress={() => setShowCreateModal(true)}>
          <Ionicons name="add" size={24} color="#6366f1" />
        </TouchableOpacity>
      </View>

      {/* Guidelines */}
      <View style={styles.guidelines}>
        <Ionicons name="shield-checkmark" size={20} color="#10b981" />
        <Text style={styles.guidelinesText}>
          Safe space for anonymous sharing. Be kind and supportive.
        </Text>
      </View>

      {/* Confessions List */}
      <FlatList
        data={confessions}
        renderItem={renderConfession}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshing={isLoading}
        onRefresh={loadConfessions}
        showsVerticalScrollIndicator={false}
      />

      {/* Create Confession Modal */}
      <Modal
        visible={showCreateModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={styles.modalContent}
          >
            {/* Modal Header */}
            <View style={styles.modalHeader}>
              <TouchableOpacity onPress={() => setShowCreateModal(false)}>
                <Text style={styles.cancelButton}>Cancel</Text>
              </TouchableOpacity>
              <Text style={styles.modalTitle}>Share Confession</Text>
              <TouchableOpacity
                onPress={handleCreateConfession}
                disabled={isSubmitting}
              >
                <Text style={[styles.shareButton, isSubmitting && styles.disabledButton]}>
                  {isSubmitting ? 'Sharing...' : 'Share'}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Category Selection */}
            <View style={styles.categorySelection}>
              <Text style={styles.sectionTitle}>Category</Text>
              <View style={styles.categoryGrid}>
                {categories.map((category) => (
                  <TouchableOpacity
                    key={category.id}
                    style={[
                      styles.categoryChip,
                      selectedCategory === category.id && styles.selectedCategoryChip,
                    ]}
                    onPress={() => setSelectedCategory(category.id)}
                  >
                    <Ionicons
                      name={category.icon as any}
                      size={16}
                      color={selectedCategory === category.id ? '#ffffff' : '#6366f1'}
                    />
                    <Text
                      style={[
                        styles.categoryChipText,
                        selectedCategory === category.id && styles.selectedCategoryChipText,
                      ]}
                    >
                      {category.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Confession Input */}
            <View style={styles.inputSection}>
              <Text style={styles.sectionTitle}>Your Confession</Text>
              <TextInput
                style={styles.confessionInput}
                placeholder="Share what's on your mind... This is a safe space."
                value={newConfession}
                onChangeText={setNewConfession}
                multiline
                maxLength={1000}
                textAlignVertical="top"
              />
              <Text style={styles.characterCount}>
                {newConfession.length}/1000 characters
              </Text>
            </View>

            {/* Options */}
            <View style={styles.optionsSection}>
              <View style={styles.optionRow}>
                <View>
                  <Text style={styles.optionTitle}>Anonymous</Text>
                  <Text style={styles.optionDescription}>
                    Your identity will be completely hidden
                  </Text>
                </View>
                <Switch
                  value={isAnonymous}
                  onValueChange={setIsAnonymous}
                  trackColor={{ false: '#e5e7eb', true: '#6366f1' }}
                  thumbColor={isAnonymous ? '#ffffff' : '#f3f4f6'}
                />
              </View>

              <View style={styles.optionRow}>
                <View>
                  <Text style={styles.optionTitle}>Request Support</Text>
                  <Text style={styles.optionDescription}>
                    Ask the community for encouragement and advice
                  </Text>
                </View>
                <Switch
                  value={supportRequested}
                  onValueChange={setSupportRequested}
                  trackColor={{ false: '#e5e7eb', true: '#6366f1' }}
                  thumbColor={supportRequested ? '#ffffff' : '#f3f4f6'}
                />
              </View>
            </View>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  guidelines: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#f0fdf4',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  guidelinesText: {
    fontSize: 14,
    color: '#166534',
    marginLeft: 8,
  },
  listContainer: {
    padding: 16,
  },
  confessionCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  confessionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryText: {
    fontSize: 12,
    color: '#6366f1',
    fontWeight: '500',
    marginLeft: 4,
  },
  confessionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  timeText: {
    fontSize: 12,
    color: '#6b7280',
  },
  confessionContent: {
    fontSize: 16,
    color: '#1f2937',
    lineHeight: 24,
    marginBottom: 12,
  },
  supportBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fef2f2',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    alignSelf: 'flex-start',
    marginBottom: 12,
  },
  supportText: {
    fontSize: 12,
    color: '#ef4444',
    fontWeight: '500',
    marginLeft: 4,
  },
  confessionActions: {
    flexDirection: 'row',
    gap: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  actionText: {
    fontSize: 14,
    color: '#6366f1',
    fontWeight: '500',
  },
  responsesContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  responseItem: {
    backgroundColor: '#f8fafc',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  responseContent: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
  },
  responseTime: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
  counselorBadge: {
    color: '#10b981',
    fontWeight: '500',
  },
  viewMoreButton: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  viewMoreText: {
    fontSize: 14,
    color: '#6366f1',
    fontWeight: '500',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  modalContent: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  cancelButton: {
    fontSize: 16,
    color: '#6b7280',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  shareButton: {
    fontSize: 16,
    color: '#6366f1',
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.5,
  },
  categorySelection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#6366f1',
    backgroundColor: '#ffffff',
  },
  selectedCategoryChip: {
    backgroundColor: '#6366f1',
  },
  categoryChipText: {
    fontSize: 14,
    color: '#6366f1',
    marginLeft: 4,
  },
  selectedCategoryChipText: {
    color: '#ffffff',
  },
  inputSection: {
    padding: 20,
  },
  confessionInput: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#1f2937',
    minHeight: 120,
    backgroundColor: '#f8fafc',
  },
  characterCount: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'right',
    marginTop: 8,
  },
  optionsSection: {
    padding: 20,
  },
  optionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
  },
  optionDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
});

export default ConfessionsRoomScreen;
