import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAppSelector, useAppDispatch } from '@/store';
import { postsAPI } from '@/services/api';
import { Post, Reaction } from '@types/index';

interface PostItem extends Post {
  author: {
    id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
    isAnonymous?: boolean;
  };
  userReaction?: Reaction;
}

const SocialFeedScreen: React.FC = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);

  const [posts, setPosts] = useState<PostItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newPostContent, setNewPostContent] = useState('');
  const [newPostCategory, setNewPostCategory] = useState('general');
  const [newPostPrivacy, setNewPostPrivacy] = useState('public');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const categories = [
    { id: 'all', name: 'All', icon: 'grid-outline' },
    { id: 'general', name: 'General', icon: 'chatbubble-outline' },
    { id: 'mental_health', name: 'Mental Health', icon: 'heart-outline' },
    { id: 'education', name: 'Education', icon: 'school-outline' },
    { id: 'social', name: 'Social', icon: 'people-outline' },
  ];

  const privacyOptions = [
    { id: 'public', name: 'Public', description: 'Everyone can see this post' },
    { id: 'friends', name: 'Friends', description: 'Only your connections can see this' },
    { id: 'anonymous', name: 'Anonymous', description: 'Post without revealing your identity' },
  ];

  useEffect(() => {
    loadPosts();
  }, [selectedCategory]);

  const loadPosts = async () => {
    try {
      setIsLoading(true);
      const category = selectedCategory === 'all' ? undefined : selectedCategory;
      const response = await postsAPI.getFeed(1, 20, category);
      setPosts(response.data.posts);
    } catch (error) {
      console.error('Error loading posts:', error);
      Alert.alert('Error', 'Failed to load posts');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadPosts();
    setRefreshing(false);
  };

  const handleCreatePost = async () => {
    if (!newPostContent.trim()) {
      Alert.alert('Error', 'Please write something to share');
      return;
    }

    setIsSubmitting(true);

    try {
      const postData = {
        content: newPostContent.trim(),
        category: newPostCategory,
        privacy: newPostPrivacy,
        tags: extractHashtags(newPostContent),
      };

      const response = await postsAPI.create(postData);
      
      // Add new post to the top of the list
      setPosts(prev => [response.data.post, ...prev]);
      
      // Reset form
      setNewPostContent('');
      setNewPostCategory('general');
      setNewPostPrivacy('public');
      setShowCreateModal(false);

      Alert.alert('Success', 'Your post has been shared!');
    } catch (error) {
      console.error('Error creating post:', error);
      Alert.alert('Error', 'Failed to create post');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReaction = async (postId: string, reactionType: 'like' | 'love' | 'support' | 'care') => {
    try {
      await postsAPI.react(postId, reactionType);
      
      // Update post reactions locally
      setPosts(prev => prev.map(post => {
        if (post.id === postId) {
          const existingReaction = post.userReaction;
          let newReactions = [...post.reactions];
          
          if (existingReaction) {
            // Remove existing reaction
            newReactions = newReactions.filter(r => r.userId !== user?.id);
          }
          
          // Add new reaction if different from existing
          if (!existingReaction || existingReaction.type !== reactionType) {
            newReactions.push({
              id: Date.now().toString(),
              userId: user?.id || '',
              type: reactionType,
              createdAt: new Date(),
            });
          }
          
          return {
            ...post,
            reactions: newReactions,
            userReaction: (!existingReaction || existingReaction.type !== reactionType) 
              ? { id: Date.now().toString(), userId: user?.id || '', type: reactionType, createdAt: new Date() }
              : undefined,
          };
        }
        return post;
      }));
    } catch (error) {
      console.error('Error reacting to post:', error);
      Alert.alert('Error', 'Failed to react to post');
    }
  };

  const handleComment = (postId: string) => {
    Alert.prompt(
      'Add Comment',
      'Share your thoughts on this post',
      (text) => {
        if (text && text.trim()) {
          addComment(postId, text.trim());
        }
      },
      'plain-text',
      '',
      'default'
    );
  };

  const addComment = async (postId: string, content: string) => {
    try {
      await postsAPI.comment(postId, content);
      
      // Reload posts to show new comment
      loadPosts();
    } catch (error) {
      console.error('Error adding comment:', error);
      Alert.alert('Error', 'Failed to add comment');
    }
  };

  const handleReportPost = async (postId: string) => {
    Alert.alert(
      'Report Post',
      'Why are you reporting this post?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Inappropriate Content', onPress: () => reportPost(postId, 'inappropriate') },
        { text: 'Spam', onPress: () => reportPost(postId, 'spam') },
        { text: 'Harmful Content', onPress: () => reportPost(postId, 'harmful') },
        { text: 'False Information', onPress: () => reportPost(postId, 'false_info') },
      ]
    );
  };

  const reportPost = async (postId: string, reason: string) => {
    try {
      await postsAPI.report(postId, reason);
      Alert.alert('Thank you', 'Your report has been submitted for review');
    } catch (error) {
      console.error('Error reporting post:', error);
      Alert.alert('Error', 'Failed to submit report');
    }
  };

  const extractHashtags = (text: string): string[] => {
    const hashtags = text.match(/#\w+/g);
    return hashtags ? hashtags.map(tag => tag.substring(1)) : [];
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - new Date(date).getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const getReactionIcon = (type: string) => {
    switch (type) {
      case 'like': return 'thumbs-up';
      case 'love': return 'heart';
      case 'support': return 'hand-left';
      case 'care': return 'medical';
      default: return 'thumbs-up';
    }
  };

  const getReactionColor = (type: string) => {
    switch (type) {
      case 'like': return '#3b82f6';
      case 'love': return '#ef4444';
      case 'support': return '#10b981';
      case 'care': return '#f59e0b';
      default: return '#6b7280';
    }
  };

  const renderPost = ({ item }: { item: PostItem }) => (
    <View style={styles.postCard}>
      {/* Post Header */}
      <View style={styles.postHeader}>
        <View style={styles.authorInfo}>
          {item.author.avatar ? (
            <Image source={{ uri: item.author.avatar }} style={styles.avatar} />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <Ionicons name="person" size={20} color="#6b7280" />
            </View>
          )}
          <View>
            <Text style={styles.authorName}>
              {item.privacy === 'anonymous' 
                ? 'Anonymous' 
                : `${item.author.firstName} ${item.author.lastName}`}
            </Text>
            <View style={styles.postMeta}>
              <Text style={styles.timeText}>{formatTimeAgo(item.createdAt)}</Text>
              <Text style={styles.categoryText}>• {item.category}</Text>
            </View>
          </View>
        </View>
        <TouchableOpacity onPress={() => handleReportPost(item.id)}>
          <Ionicons name="ellipsis-horizontal" size={20} color="#6b7280" />
        </TouchableOpacity>
      </View>

      {/* Post Content */}
      <Text style={styles.postContent}>{item.content}</Text>

      {/* Post Tags */}
      {item.tags && item.tags.length > 0 && (
        <View style={styles.tagsContainer}>
          {item.tags.map((tag, index) => (
            <Text key={index} style={styles.tag}>#{tag}</Text>
          ))}
        </View>
      )}

      {/* Post Actions */}
      <View style={styles.postActions}>
        <View style={styles.reactionsContainer}>
          {['like', 'love', 'support', 'care'].map((reactionType) => (
            <TouchableOpacity
              key={reactionType}
              style={[
                styles.reactionButton,
                item.userReaction?.type === reactionType && styles.activeReaction,
              ]}
              onPress={() => handleReaction(item.id, reactionType as any)}
            >
              <Ionicons
                name={getReactionIcon(reactionType) as any}
                size={18}
                color={
                  item.userReaction?.type === reactionType
                    ? getReactionColor(reactionType)
                    : '#6b7280'
                }
              />
            </TouchableOpacity>
          ))}
        </View>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleComment(item.id)}
        >
          <Ionicons name="chatbubble-outline" size={18} color="#6b7280" />
          <Text style={styles.actionText}>{item.comments?.length || 0}</Text>
        </TouchableOpacity>
      </View>

      {/* Reactions Summary */}
      {item.reactions && item.reactions.length > 0 && (
        <View style={styles.reactionsSummary}>
          <Text style={styles.reactionsText}>
            {item.reactions.length} {item.reactions.length === 1 ? 'reaction' : 'reactions'}
          </Text>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="#1f2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Social Feed</Text>
        <TouchableOpacity onPress={() => setShowCreateModal(true)}>
          <Ionicons name="add" size={24} color="#6366f1" />
        </TouchableOpacity>
      </View>

      {/* Category Filter */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoryFilter}
        contentContainerStyle={styles.categoryFilterContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryButton,
              selectedCategory === category.id && styles.activeCategoryButton,
            ]}
            onPress={() => setSelectedCategory(category.id)}
          >
            <Ionicons
              name={category.icon as any}
              size={16}
              color={selectedCategory === category.id ? '#ffffff' : '#6366f1'}
            />
            <Text
              style={[
                styles.categoryButtonText,
                selectedCategory === category.id && styles.activeCategoryButtonText,
              ]}
            >
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Posts List */}
      <FlatList
        data={posts}
        renderItem={renderPost}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />

      {/* Create Post Modal */}
      <Modal
        visible={showCreateModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          {/* Modal Header */}
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowCreateModal(false)}>
              <Text style={styles.cancelButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Create Post</Text>
            <TouchableOpacity
              onPress={handleCreatePost}
              disabled={isSubmitting}
            >
              <Text style={[styles.shareButton, isSubmitting && styles.disabledButton]}>
                {isSubmitting ? 'Posting...' : 'Post'}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Post Input */}
            <View style={styles.inputSection}>
              <TextInput
                style={styles.postInput}
                placeholder="What's on your mind? Share your thoughts, experiences, or ask for advice..."
                value={newPostContent}
                onChangeText={setNewPostContent}
                multiline
                maxLength={2000}
                textAlignVertical="top"
              />
              <Text style={styles.characterCount}>
                {newPostContent.length}/2000 characters
              </Text>
            </View>

            {/* Category Selection */}
            <View style={styles.optionSection}>
              <Text style={styles.optionTitle}>Category</Text>
              <View style={styles.optionGrid}>
                {categories.slice(1).map((category) => (
                  <TouchableOpacity
                    key={category.id}
                    style={[
                      styles.optionChip,
                      newPostCategory === category.id && styles.selectedOptionChip,
                    ]}
                    onPress={() => setNewPostCategory(category.id)}
                  >
                    <Ionicons
                      name={category.icon as any}
                      size={16}
                      color={newPostCategory === category.id ? '#ffffff' : '#6366f1'}
                    />
                    <Text
                      style={[
                        styles.optionChipText,
                        newPostCategory === category.id && styles.selectedOptionChipText,
                      ]}
                    >
                      {category.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Privacy Selection */}
            <View style={styles.optionSection}>
              <Text style={styles.optionTitle}>Privacy</Text>
              {privacyOptions.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.privacyOption,
                    newPostPrivacy === option.id && styles.selectedPrivacyOption,
                  ]}
                  onPress={() => setNewPostPrivacy(option.id)}
                >
                  <View style={styles.privacyOptionContent}>
                    <Text style={styles.privacyOptionName}>{option.name}</Text>
                    <Text style={styles.privacyOptionDescription}>{option.description}</Text>
                  </View>
                  {newPostPrivacy === option.id && (
                    <Ionicons name="checkmark-circle" size={20} color="#6366f1" />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  categoryFilter: {
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  categoryFilterContent: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    gap: 8,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#6366f1',
    backgroundColor: '#ffffff',
  },
  activeCategoryButton: {
    backgroundColor: '#6366f1',
  },
  categoryButtonText: {
    fontSize: 14,
    color: '#6366f1',
    marginLeft: 4,
  },
  activeCategoryButtonText: {
    color: '#ffffff',
  },
  listContainer: {
    padding: 16,
  },
  postCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  authorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  avatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  authorName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  postMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  timeText: {
    fontSize: 12,
    color: '#6b7280',
  },
  categoryText: {
    fontSize: 12,
    color: '#6b7280',
    marginLeft: 4,
  },
  postContent: {
    fontSize: 16,
    color: '#1f2937',
    lineHeight: 24,
    marginBottom: 12,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  tag: {
    fontSize: 14,
    color: '#6366f1',
    marginRight: 8,
    marginBottom: 4,
  },
  postActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  reactionsContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  reactionButton: {
    padding: 8,
    borderRadius: 20,
  },
  activeReaction: {
    backgroundColor: '#f0f9ff',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  actionText: {
    fontSize: 14,
    color: '#6b7280',
  },
  reactionsSummary: {
    marginTop: 8,
  },
  reactionsText: {
    fontSize: 12,
    color: '#6b7280',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  cancelButton: {
    fontSize: 16,
    color: '#6b7280',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  shareButton: {
    fontSize: 16,
    color: '#6366f1',
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.5,
  },
  modalContent: {
    flex: 1,
  },
  inputSection: {
    padding: 20,
  },
  postInput: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#1f2937',
    minHeight: 120,
    backgroundColor: '#f8fafc',
    textAlignVertical: 'top',
  },
  characterCount: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'right',
    marginTop: 8,
  },
  optionSection: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  optionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#6366f1',
    backgroundColor: '#ffffff',
  },
  selectedOptionChip: {
    backgroundColor: '#6366f1',
  },
  optionChipText: {
    fontSize: 14,
    color: '#6366f1',
    marginLeft: 4,
  },
  selectedOptionChipText: {
    color: '#ffffff',
  },
  privacyOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginBottom: 8,
  },
  selectedPrivacyOption: {
    borderColor: '#6366f1',
    backgroundColor: '#f0f9ff',
  },
  privacyOptionContent: {
    flex: 1,
  },
  privacyOptionName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
  },
  privacyOptionDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
});

export default SocialFeedScreen;
