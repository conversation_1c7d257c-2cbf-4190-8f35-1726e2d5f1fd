import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { AdminUser } from '@types/index';
import { useAppSelector, useAppDispatch } from '@/store';
import { adminAPI } from '@/services/api';

const { width } = Dimensions.get('window');

interface AdminDashboardProps {
  user: AdminUser;
}

interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  pendingVerifications: number;
  activeSOS: number;
  totalPosts: number;
  flaggedContent: number;
  monthlyGrowth: number;
  responseTime: number;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ user }) => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();

  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    activeUsers: 0,
    pendingVerifications: 0,
    activeSOS: 0,
    totalPosts: 0,
    flaggedContent: 0,
    monthlyGrowth: 0,
    responseTime: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      const response = await adminAPI.getAnalytics();
      setStats(response.data.analytics);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const navigateToUserManagement = () => {
    navigation.navigate('UserManagement' as never);
  };

  const navigateToVerifications = () => {
    navigation.navigate('ActivistVerifications' as never);
  };

  const navigateToContentModeration = () => {
    navigation.navigate('ContentModeration' as never);
  };

  const navigateToSOSMonitoring = () => {
    navigation.navigate('SOSMonitoring' as never);
  };

  const navigateToAnalytics = () => {
    navigation.navigate('AdminAnalytics' as never);
  };

  const navigateToSystemSettings = () => {
    navigation.navigate('SystemSettings' as never);
  };

  const getStatColor = (value: number, threshold: number, isGood: boolean = true) => {
    if (isGood) {
      return value >= threshold ? '#10b981' : '#ef4444';
    } else {
      return value <= threshold ? '#10b981' : '#ef4444';
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>Admin Dashboard</Text>
            <Text style={styles.subtitle}>
              {user.adminLevel === 'super_admin' ? 'Super Administrator' : 'Moderator'}
            </Text>
          </View>
          <TouchableOpacity onPress={navigateToSystemSettings}>
            <Ionicons name="settings-outline" size={24} color="#6366f1" />
          </TouchableOpacity>
        </View>

        {/* Key Metrics */}
        <View style={styles.metricsContainer}>
          <Text style={styles.sectionTitle}>Key Metrics</Text>
          <View style={styles.metricsGrid}>
            <View style={styles.metricCard}>
              <Text style={styles.metricNumber}>{formatNumber(stats.totalUsers)}</Text>
              <Text style={styles.metricLabel}>Total Users</Text>
              <View style={styles.metricChange}>
                <Ionicons name="trending-up" size={12} color="#10b981" />
                <Text style={[styles.metricChangeText, { color: '#10b981' }]}>
                  +{stats.monthlyGrowth}%
                </Text>
              </View>
            </View>

            <View style={styles.metricCard}>
              <Text style={styles.metricNumber}>{formatNumber(stats.activeUsers)}</Text>
              <Text style={styles.metricLabel}>Active Users</Text>
              <Text style={styles.metricSubtext}>Last 24h</Text>
            </View>

            <View style={styles.metricCard}>
              <Text style={[styles.metricNumber, { color: getStatColor(stats.pendingVerifications, 10, false) }]}>
                {stats.pendingVerifications}
              </Text>
              <Text style={styles.metricLabel}>Pending Verifications</Text>
              <Text style={styles.metricSubtext}>Activists</Text>
            </View>

            <View style={styles.metricCard}>
              <Text style={[styles.metricNumber, { color: getStatColor(stats.activeSOS, 5, false) }]}>
                {stats.activeSOS}
              </Text>
              <Text style={styles.metricLabel}>Active SOS</Text>
              <Text style={styles.metricSubtext}>Emergency</Text>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity 
              style={styles.quickActionCard}
              onPress={navigateToUserManagement}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: '#dbeafe' }]}>
                <Ionicons name="people" size={24} color="#3b82f6" />
              </View>
              <Text style={styles.quickActionTitle}>User Management</Text>
              <Text style={styles.quickActionSubtitle}>Manage user accounts</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.quickActionCard}
              onPress={navigateToVerifications}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: '#fef3c7' }]}>
                <Ionicons name="shield-checkmark" size={24} color="#f59e0b" />
              </View>
              <Text style={styles.quickActionTitle}>Verifications</Text>
              <Text style={styles.quickActionSubtitle}>
                {stats.pendingVerifications} pending
              </Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.quickActionCard}
              onPress={navigateToContentModeration}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: '#fecaca' }]}>
                <Ionicons name="flag" size={24} color="#ef4444" />
              </View>
              <Text style={styles.quickActionTitle}>Content Moderation</Text>
              <Text style={styles.quickActionSubtitle}>
                {stats.flaggedContent} flagged items
              </Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.quickActionCard}
              onPress={navigateToSOSMonitoring}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: '#fed7d7' }]}>
                <Ionicons name="warning" size={24} color="#dc2626" />
              </View>
              <Text style={styles.quickActionTitle}>SOS Monitoring</Text>
              <Text style={styles.quickActionSubtitle}>
                {stats.activeSOS} active emergencies
              </Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.quickActionCard}
              onPress={navigateToAnalytics}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: '#e0e7ff' }]}>
                <Ionicons name="analytics" size={24} color="#6366f1" />
              </View>
              <Text style={styles.quickActionTitle}>Analytics</Text>
              <Text style={styles.quickActionSubtitle}>Platform insights</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.quickActionCard}
              onPress={navigateToSystemSettings}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: '#f3e8ff' }]}>
                <Ionicons name="settings" size={24} color="#8b5cf6" />
              </View>
              <Text style={styles.quickActionTitle}>System Settings</Text>
              <Text style={styles.quickActionSubtitle}>Configure platform</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.recentActivityContainer}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          <View style={styles.activityCard}>
            <View style={styles.activityItem}>
              <View style={styles.activityIcon}>
                <Ionicons name="person-add" size={16} color="#10b981" />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>New user registration</Text>
                <Text style={styles.activityTime}>2 minutes ago</Text>
              </View>
            </View>

            <View style={styles.activityItem}>
              <View style={styles.activityIcon}>
                <Ionicons name="warning" size={16} color="#ef4444" />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>SOS request resolved</Text>
                <Text style={styles.activityTime}>15 minutes ago</Text>
              </View>
            </View>

            <View style={styles.activityItem}>
              <View style={styles.activityIcon}>
                <Ionicons name="shield-checkmark" size={16} color="#f59e0b" />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>Activist verified</Text>
                <Text style={styles.activityTime}>1 hour ago</Text>
              </View>
            </View>

            <TouchableOpacity style={styles.viewAllButton}>
              <Text style={styles.viewAllButtonText}>View All Activity</Text>
              <Ionicons name="chevron-forward" size={16} color="#6366f1" />
            </TouchableOpacity>
          </View>
        </View>

        {/* System Health */}
        <View style={styles.systemHealthContainer}>
          <Text style={styles.sectionTitle}>System Health</Text>
          <View style={styles.healthCard}>
            <View style={styles.healthMetric}>
              <Text style={styles.healthLabel}>Server Status</Text>
              <View style={styles.healthStatus}>
                <View style={[styles.healthIndicator, { backgroundColor: '#10b981' }]} />
                <Text style={styles.healthValue}>Operational</Text>
              </View>
            </View>

            <View style={styles.healthMetric}>
              <Text style={styles.healthLabel}>Response Time</Text>
              <View style={styles.healthStatus}>
                <Text style={styles.healthValue}>{stats.responseTime}ms</Text>
              </View>
            </View>

            <View style={styles.healthMetric}>
              <Text style={styles.healthLabel}>Database</Text>
              <View style={styles.healthStatus}>
                <View style={[styles.healthIndicator, { backgroundColor: '#10b981' }]} />
                <Text style={styles.healthValue}>Connected</Text>
              </View>
            </View>

            <View style={styles.healthMetric}>
              <Text style={styles.healthLabel}>Storage</Text>
              <View style={styles.healthStatus}>
                <Text style={styles.healthValue}>78% used</Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  metricsContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  metricCard: {
    width: (width - 56) / 2,
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  metricNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  metricLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  metricSubtext: {
    fontSize: 12,
    color: '#9ca3af',
    marginTop: 2,
  },
  metricChange: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  metricChangeText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  quickActionsContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickActionCard: {
    width: (width - 56) / 2,
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  quickActionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    textAlign: 'center',
    marginBottom: 4,
  },
  quickActionSubtitle: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
  },
  recentActivityContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  activityCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
  },
  activityTime: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginTop: 8,
  },
  viewAllButtonText: {
    fontSize: 14,
    color: '#6366f1',
    fontWeight: '500',
    marginRight: 4,
  },
  systemHealthContainer: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  healthCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  healthMetric: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  healthLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  healthStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  healthIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  healthValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
  },
});

export default AdminDashboard;
