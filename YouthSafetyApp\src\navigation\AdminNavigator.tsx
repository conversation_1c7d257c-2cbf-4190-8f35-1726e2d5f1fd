import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { RootStackParamList } from '@types/index';

// Import admin screens
import AdminDashboardScreen from '@screens/admin/AdminDashboardScreen';
import UserManagementScreen from '@screens/admin/UserManagementScreen';
import ActivistVerificationsScreen from '@screens/admin/ActivistVerificationsScreen';
import ContentModerationScreen from '@screens/admin/ContentModerationScreen';
import SOSMonitoringScreen from '@screens/admin/SOSMonitoringScreen';
import ProfileScreen from '@screens/profile/ProfileScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator<RootStackParamList>();

const AdminTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Dashboard') {
            iconName = focused ? 'speedometer' : 'speedometer-outline';
          } else if (route.name === 'Users') {
            iconName = focused ? 'people' : 'people-outline';
          } else if (route.name === 'Verifications') {
            iconName = focused ? 'checkmark-circle' : 'checkmark-circle-outline';
          } else if (route.name === 'Moderation') {
            iconName = focused ? 'flag' : 'flag-outline';
          } else if (route.name === 'SOS') {
            iconName = focused ? 'warning' : 'warning-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#dc3545',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
        tabBarLabelStyle: { fontSize: 10 },
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={AdminDashboardScreen}
        options={{ title: 'Dashboard' }}
      />
      <Tab.Screen 
        name="Users" 
        component={UserManagementScreen}
        options={{ title: 'Users' }}
      />
      <Tab.Screen 
        name="Verifications" 
        component={ActivistVerificationsScreen}
        options={{ title: 'Verify' }}
      />
      <Tab.Screen 
        name="Moderation" 
        component={ContentModerationScreen}
        options={{ title: 'Moderate' }}
      />
      <Tab.Screen 
        name="SOS" 
        component={SOSMonitoringScreen}
        options={{ title: 'SOS' }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{ title: 'Profile' }}
      />
    </Tab.Navigator>
  );
};

const AdminNavigator: React.FC = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="AdminTabs" component={AdminTabNavigator} />
    </Stack.Navigator>
  );
};

export default AdminNavigator;
