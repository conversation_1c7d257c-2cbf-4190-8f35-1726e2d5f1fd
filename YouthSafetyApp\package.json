{"name": "youth-safety-app", "version": "1.0.0", "description": "A comprehensive mobile app for youth safety, social engagement, and community activism", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "detox test", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "build": "expo build", "build:android": "expo build:android", "build:ios": "expo build:ios", "publish": "expo publish", "clean": "expo r -c", "postinstall": "patch-package"}, "dependencies": {"expo": "~49.0.0", "react": "18.2.0", "react-native": "0.72.6", "@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/netinfo": "9.3.10", "@react-native-picker/picker": "2.4.10", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@react-navigation/bottom-tabs": "^6.5.8", "@reduxjs/toolkit": "^1.9.5", "axios": "^1.5.0", "crypto-js": "^4.1.1", "expo-av": "~13.4.1", "expo-camera": "~13.4.2", "expo-constants": "~14.4.2", "expo-device": "~5.4.0", "expo-file-system": "~15.4.3", "expo-image-picker": "~14.3.2", "expo-local-authentication": "~13.4.1", "expo-location": "~16.1.0", "expo-media-library": "~15.4.1", "expo-notifications": "~0.20.1", "expo-secure-store": "~12.3.1", "expo-status-bar": "~1.6.0", "react-native-gesture-handler": "~2.12.0", "react-native-maps": "1.7.1", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0", "react-native-webrtc": "^111.0.3", "react-redux": "^8.1.2", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "socket.io-client": "^4.7.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.22.0", "@babel/preset-react": "^7.22.0", "@babel/preset-typescript": "^7.22.0", "@testing-library/jest-native": "^5.4.2", "@testing-library/react-native": "^12.1.2", "@types/crypto-js": "^4.1.1", "@types/jest": "^29.5.3", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@types/react-test-renderer": "^18.0.0", "@types/redux-logger": "^3.0.9", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "babel-jest": "^29.6.1", "detox": "^20.10.1", "eslint": "^8.45.0", "eslint-config-expo": "^7.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.0.0", "jest": "^29.6.1", "jest-expo": "~49.0.0", "patch-package": "^8.0.0", "react-test-renderer": "18.2.0", "typescript": "^5.1.3"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect", "<rootDir>/jest.setup.js"], "testPathIgnorePatterns": ["<rootDir>/node_modules/", "<rootDir>/.expo/", "<rootDir>/dist/"], "collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/**/*.stories.{ts,tsx}", "!src/**/__tests__/**", "!src/**/node_modules/**"], "coverageReporters": ["text", "lcov", "html"], "coverageDirectory": "coverage", "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1", "^@types/(.*)$": "<rootDir>/src/types/$1", "^@components/(.*)$": "<rootDir>/src/components/$1", "^@screens/(.*)$": "<rootDir>/src/screens/$1", "^@services/(.*)$": "<rootDir>/src/services/$1", "^@store/(.*)$": "<rootDir>/src/store/$1", "^@utils/(.*)$": "<rootDir>/src/utils/$1"}, "transform": {"^.+\\.(js|jsx|ts|tsx)$": "babel-jest"}, "transformIgnorePatterns": ["node_modules/(?!(jest-)?@?react-native|@react-native-community|@react-navigation|expo|@expo|@unimodules|unimodules|sentry-expo|native-base|react-clone-referenced-element)"], "testEnvironment": "jsdom", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"], "globals": {"__DEV__": true}, "testMatch": ["**/__tests__/**/*.(ts|tsx|js)", "**/*.(test|spec).(ts|tsx|js)"], "verbose": true, "clearMocks": true, "resetMocks": true, "restoreMocks": true}, "eslintConfig": {"extends": ["expo", "@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:react-native/all"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "react", "react-hooks", "react-native"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": "error", "react-native/no-inline-styles": "warn", "react-native/split-platform-components": "error"}, "settings": {"react": {"version": "detect"}}}, "private": true, "keywords": ["youth", "safety", "emergency", "sos", "react-native", "expo", "mobile-app"], "author": "Youth Safety Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/youth-safety-app.git"}, "bugs": {"url": "https://github.com/your-org/youth-safety-app/issues"}, "homepage": "https://github.com/your-org/youth-safety-app#readme"}