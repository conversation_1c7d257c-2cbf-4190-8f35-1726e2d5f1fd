import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Linking,
  Dimensions,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import MapView, { <PERSON><PERSON>, <PERSON>yl<PERSON> } from 'react-native-maps';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useAppSelector, useAppDispatch } from '@/store';
import { locationService } from '@/services/locationService';
import { webrtcService } from '@/services/webrtcService';
import { sosAPI } from '@/services/api';
import { SOSRequest, LocationData } from '@types/index';

const { width, height } = Dimensions.get('window');

interface SOSResponseScreenProps {
  route: {
    params: {
      sosId: string;
      sosRequest?: SOSRequest;
    };
  };
}

const SOSResponseScreen: React.FC<SOSResponseScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useAppDispatch();
  
  const { user } = useAppSelector((state) => state.auth);
  const [sosRequest, setSosRequest] = useState<SOSRequest | null>(route.params?.sosRequest || null);
  const [myLocation, setMyLocation] = useState<LocationData | null>(null);
  const [isNavigating, setIsNavigating] = useState(false);
  const [estimatedArrival, setEstimatedArrival] = useState<number | null>(null);
  const [isCallActive, setIsCallActive] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);
  
  const mapRef = useRef<MapView>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    loadSOSDetails();
    startLocationTracking();
    startTimer();
    
    return () => {
      stopLocationTracking();
      stopTimer();
    };
  }, []);

  const loadSOSDetails = async () => {
    if (!sosRequest && route.params?.sosId) {
      try {
        // Load SOS details from API
        // const response = await sosAPI.getById(route.params.sosId);
        // setSosRequest(response.data.sosRequest);
      } catch (error) {
        console.error('Error loading SOS details:', error);
        Alert.alert('Error', 'Failed to load emergency details');
      }
    }
  };

  const startLocationTracking = async () => {
    try {
      await locationService.startLocationTracking(
        (location) => {
          setMyLocation(location);
          
          if (sosRequest) {
            // Calculate ETA
            const distance = locationService.calculateDistance(
              location.latitude,
              location.longitude,
              sosRequest.latitude,
              sosRequest.longitude
            );
            
            // Rough ETA calculation (assuming 30 km/h average speed)
            const eta = Math.round((distance / 30) * 60); // minutes
            setEstimatedArrival(eta);
          }
        },
        {
          timeInterval: 5000, // Update every 5 seconds
          distanceInterval: 10, // Update every 10 meters
        }
      );
    } catch (error) {
      console.error('Error starting location tracking:', error);
    }
  };

  const stopLocationTracking = async () => {
    await locationService.stopLocationTracking();
  };

  const startTimer = () => {
    timerRef.current = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);
  };

  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  const handleStartNavigation = () => {
    if (!sosRequest) return;

    const url = `https://maps.google.com/maps?daddr=${sosRequest.latitude},${sosRequest.longitude}`;
    
    Alert.alert(
      'Open Navigation',
      'This will open Google Maps for turn-by-turn directions.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Open Maps',
          onPress: () => {
            Linking.openURL(url);
            setIsNavigating(true);
          },
        },
      ]
    );
  };

  const handleCallYouth = async () => {
    if (!sosRequest) return;

    try {
      setIsCallActive(true);
      await webrtcService.initializeCall(sosRequest.userId, 'audio');
    } catch (error) {
      console.error('Error initiating call:', error);
      Alert.alert('Error', 'Failed to initiate call');
      setIsCallActive(false);
    }
  };

  const handleVideoCall = async () => {
    if (!sosRequest) return;

    try {
      setIsCallActive(true);
      await webrtcService.initializeCall(sosRequest.userId, 'video');
    } catch (error) {
      console.error('Error initiating video call:', error);
      Alert.alert('Error', 'Failed to initiate video call');
      setIsCallActive(false);
    }
  };

  const handleCallEmergencyServices = () => {
    Alert.alert(
      'Call Emergency Services',
      'Do you want to call 911/emergency services?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Call 911',
          style: 'destructive',
          onPress: () => Linking.openURL('tel:911'),
        },
      ]
    );
  };

  const handleMarkAsResolved = () => {
    Alert.alert(
      'Mark as Resolved',
      'Are you sure the emergency has been resolved?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Mark Resolved',
          onPress: resolveEmergency,
        },
      ]
    );
  };

  const resolveEmergency = async () => {
    if (!sosRequest) return;

    try {
      await sosAPI.resolve(sosRequest.id, 'Emergency resolved by activist');
      
      Alert.alert(
        'Emergency Resolved',
        'The emergency has been marked as resolved. Thank you for your service!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      console.error('Error resolving emergency:', error);
      Alert.alert('Error', 'Failed to resolve emergency');
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getEmergencyTypeColor = (type: string) => {
    switch (type) {
      case 'medical': return '#ef4444';
      case 'safety': return '#f59e0b';
      case 'mental_health': return '#8b5cf6';
      default: return '#6b7280';
    }
  };

  if (!sosRequest) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading emergency details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="#1f2937" />
        </TouchableOpacity>
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>Emergency Response</Text>
          <Text style={styles.headerSubtitle}>Active for {formatTime(elapsedTime)}</Text>
        </View>
        <View style={styles.emergencyBadge}>
          <Text style={styles.emergencyBadgeText}>ACTIVE</Text>
        </View>
      </View>

      {/* Map */}
      <View style={styles.mapContainer}>
        <MapView
          ref={mapRef}
          style={styles.map}
          showsUserLocation={true}
          followsUserLocation={true}
          initialRegion={{
            latitude: sosRequest.latitude,
            longitude: sosRequest.longitude,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          }}
        >
          {/* SOS Location Marker */}
          <Marker
            coordinate={{
              latitude: sosRequest.latitude,
              longitude: sosRequest.longitude,
            }}
            title="Emergency Location"
            description={sosRequest.address}
          >
            <View style={[styles.sosMarker, { backgroundColor: getEmergencyTypeColor(sosRequest.emergencyType) }]}>
              <Ionicons name="warning" size={24} color="#ffffff" />
            </View>
          </Marker>

          {/* Route line if both locations available */}
          {myLocation && (
            <Polyline
              coordinates={[
                { latitude: myLocation.latitude, longitude: myLocation.longitude },
                { latitude: sosRequest.latitude, longitude: sosRequest.longitude },
              ]}
              strokeColor="#6366f1"
              strokeWidth={3}
              lineDashPattern={[5, 5]}
            />
          )}
        </MapView>

        {/* ETA Overlay */}
        {estimatedArrival && (
          <View style={styles.etaOverlay}>
            <Text style={styles.etaText}>ETA: {estimatedArrival} min</Text>
          </View>
        )}
      </View>

      {/* Emergency Details */}
      <ScrollView style={styles.detailsContainer}>
        <View style={styles.emergencyCard}>
          <View style={styles.emergencyHeader}>
            <View style={styles.emergencyTypeContainer}>
              <Ionicons 
                name="medical" 
                size={20} 
                color={getEmergencyTypeColor(sosRequest.emergencyType)} 
              />
              <Text style={[styles.emergencyType, { color: getEmergencyTypeColor(sosRequest.emergencyType) }]}>
                {sosRequest.emergencyType.replace('_', ' ').toUpperCase()}
              </Text>
            </View>
            <Text style={styles.priorityBadge}>{sosRequest.priority?.toUpperCase()}</Text>
          </View>

          <View style={styles.locationInfo}>
            <Ionicons name="location" size={16} color="#6b7280" />
            <Text style={styles.locationText}>{sosRequest.address}</Text>
          </View>

          {sosRequest.description && (
            <View style={styles.descriptionContainer}>
              <Text style={styles.descriptionLabel}>Description:</Text>
              <Text style={styles.descriptionText}>{sosRequest.description}</Text>
            </View>
          )}

          {/* Medical Information */}
          {sosRequest.medicalInfo && Object.keys(sosRequest.medicalInfo).length > 0 && (
            <View style={styles.medicalInfoContainer}>
              <Text style={styles.medicalInfoLabel}>Medical Information:</Text>
              {sosRequest.medicalInfo.bloodType && (
                <Text style={styles.medicalInfoText}>Blood Type: {sosRequest.medicalInfo.bloodType}</Text>
              )}
              {sosRequest.medicalInfo.allergies && sosRequest.medicalInfo.allergies.length > 0 && (
                <Text style={styles.medicalInfoText}>
                  Allergies: {sosRequest.medicalInfo.allergies.join(', ')}
                </Text>
              )}
              {sosRequest.medicalInfo.medications && sosRequest.medicalInfo.medications.length > 0 && (
                <Text style={styles.medicalInfoText}>
                  Medications: {sosRequest.medicalInfo.medications.join(', ')}
                </Text>
              )}
            </View>
          )}
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <View style={styles.actionRow}>
          <TouchableOpacity
            style={styles.navigationButton}
            onPress={handleStartNavigation}
          >
            <Ionicons name="navigate" size={20} color="#ffffff" />
            <Text style={styles.actionButtonText}>Navigate</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.callButton}
            onPress={handleCallYouth}
          >
            <Ionicons name="call" size={20} color="#ffffff" />
            <Text style={styles.actionButtonText}>Call</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.videoButton}
            onPress={handleVideoCall}
          >
            <Ionicons name="videocam" size={20} color="#ffffff" />
            <Text style={styles.actionButtonText}>Video</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.actionRow}>
          <TouchableOpacity
            style={styles.emergencyCallButton}
            onPress={handleCallEmergencyServices}
          >
            <Ionicons name="medical" size={20} color="#ffffff" />
            <Text style={styles.actionButtonText}>Call 911</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.resolveButton}
            onPress={handleMarkAsResolved}
          >
            <Ionicons name="checkmark-circle" size={20} color="#ffffff" />
            <Text style={styles.actionButtonText}>Mark Resolved</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#6b7280',
  },
  emergencyBadge: {
    backgroundColor: '#ef4444',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  emergencyBadgeText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
  mapContainer: {
    height: height * 0.4,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  sosMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  etaOverlay: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  etaText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  detailsContainer: {
    flex: 1,
    padding: 16,
  },
  emergencyCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  emergencyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  emergencyTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  emergencyType: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  priorityBadge: {
    fontSize: 12,
    fontWeight: '600',
    color: '#f59e0b',
    backgroundColor: '#fef3c7',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  locationText: {
    fontSize: 14,
    color: '#374151',
    marginLeft: 8,
    flex: 1,
  },
  descriptionContainer: {
    marginBottom: 12,
  },
  descriptionLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  descriptionText: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
  },
  medicalInfoContainer: {
    backgroundColor: '#fef2f2',
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#ef4444',
  },
  medicalInfoLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#991b1b',
    marginBottom: 8,
  },
  medicalInfoText: {
    fontSize: 14,
    color: '#7f1d1d',
    marginBottom: 4,
  },
  actionContainer: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  actionRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  navigationButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#6366f1',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  callButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#10b981',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  videoButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#8b5cf6',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  emergencyCallButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ef4444',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  resolveButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#059669',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  actionButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default SOSResponseScreen;
