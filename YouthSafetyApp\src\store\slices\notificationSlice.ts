import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Notification } from '@types/index';

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  lastFetchedAt: string | null;
  hasMore: boolean;
  page: number;
}

const initialState: NotificationState = {
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,
  lastFetchedAt: null,
  hasMore: true,
  page: 1,
};

// Async thunks
export const fetchNotifications = createAsyncThunk(
  'notifications/fetchNotifications',
  async ({ page = 1, limit = 20 }: { page?: number; limit?: number } = {}, { rejectWithValue }) => {
    try {
      // This would be replaced with actual API call
      const response = await fetch(`/api/notifications?page=${page}&limit=${limit}`);
      if (!response.ok) {
        throw new Error('Failed to fetch notifications');
      }
      const data = await response.json();
      return { notifications: data.notifications, hasMore: data.hasMore, page };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const markNotificationAsRead = createAsyncThunk(
  'notifications/markAsRead',
  async (notificationId: string, { rejectWithValue }) => {
    try {
      // This would be replaced with actual API call
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST',
      });
      if (!response.ok) {
        throw new Error('Failed to mark notification as read');
      }
      return notificationId;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const markAllNotificationsAsRead = createAsyncThunk(
  'notifications/markAllAsRead',
  async (_, { rejectWithValue }) => {
    try {
      // This would be replaced with actual API call
      const response = await fetch('/api/notifications/read-all', {
        method: 'POST',
      });
      if (!response.ok) {
        throw new Error('Failed to mark all notifications as read');
      }
      return true;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const deleteNotification = createAsyncThunk(
  'notifications/deleteNotification',
  async (notificationId: string, { rejectWithValue }) => {
    try {
      // This would be replaced with actual API call
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE',
      });
      if (!response.ok) {
        throw new Error('Failed to delete notification');
      }
      return notificationId;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const notificationSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    // Real-time notification handling
    addNotification: (state, action: PayloadAction<Notification>) => {
      // Check if notification already exists
      const existingIndex = state.notifications.findIndex(n => n.id === action.payload.id);
      if (existingIndex === -1) {
        state.notifications.unshift(action.payload);
        if (!action.payload.isRead) {
          state.unreadCount += 1;
        }
      }
    },

    // Local state updates (optimistic updates)
    markAsReadLocally: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification && !notification.isRead) {
        notification.isRead = true;
        state.unreadCount = Math.max(0, state.unreadCount - 1);
      }
    },

    markAllAsReadLocally: (state) => {
      state.notifications.forEach(n => {
        n.isRead = true;
      });
      state.unreadCount = 0;
    },

    removeNotificationLocally: (state, action: PayloadAction<string>) => {
      const index = state.notifications.findIndex(n => n.id === action.payload);
      if (index !== -1) {
        const notification = state.notifications[index];
        if (!notification.isRead) {
          state.unreadCount = Math.max(0, state.unreadCount - 1);
        }
        state.notifications.splice(index, 1);
      }
    },

    // Utility actions
    clearAllNotifications: (state) => {
      state.notifications = [];
      state.unreadCount = 0;
      state.hasMore = true;
      state.page = 1;
    },

    updateNotificationPriority: (state, action: PayloadAction<{ id: string; priority: 'low' | 'medium' | 'high' | 'urgent' }>) => {
      const notification = state.notifications.find(n => n.id === action.payload.id);
      if (notification) {
        notification.priority = action.payload.priority;
      }
    },

    filterNotificationsByType: (state, action: PayloadAction<string[]>) => {
      const allowedTypes = action.payload;
      state.notifications = state.notifications.filter(n => allowedTypes.includes(n.type));
      state.unreadCount = state.notifications.filter(n => !n.isRead).length;
    },

    // Error handling
    clearError: (state) => {
      state.error = null;
    },

    // Reset state
    resetNotifications: () => initialState,
  },
  extraReducers: (builder) => {
    // Fetch notifications
    builder
      .addCase(fetchNotifications.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchNotifications.fulfilled, (state, action) => {
        state.isLoading = false;
        const { notifications, hasMore, page } = action.payload;
        
        if (page === 1) {
          state.notifications = notifications;
        } else {
          state.notifications.push(...notifications);
        }
        
        state.hasMore = hasMore;
        state.page = page;
        state.unreadCount = state.notifications.filter(n => !n.isRead).length;
        state.lastFetchedAt = new Date().toISOString();
      })
      .addCase(fetchNotifications.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Mark as read
    builder
      .addCase(markNotificationAsRead.fulfilled, (state, action) => {
        const notification = state.notifications.find(n => n.id === action.payload);
        if (notification && !notification.isRead) {
          notification.isRead = true;
          state.unreadCount = Math.max(0, state.unreadCount - 1);
        }
      })
      .addCase(markNotificationAsRead.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Mark all as read
    builder
      .addCase(markAllNotificationsAsRead.fulfilled, (state) => {
        state.notifications.forEach(n => {
          n.isRead = true;
        });
        state.unreadCount = 0;
      })
      .addCase(markAllNotificationsAsRead.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Delete notification
    builder
      .addCase(deleteNotification.fulfilled, (state, action) => {
        const index = state.notifications.findIndex(n => n.id === action.payload);
        if (index !== -1) {
          const notification = state.notifications[index];
          if (!notification.isRead) {
            state.unreadCount = Math.max(0, state.unreadCount - 1);
          }
          state.notifications.splice(index, 1);
        }
      })
      .addCase(deleteNotification.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const {
  addNotification,
  markAsReadLocally,
  markAllAsReadLocally,
  removeNotificationLocally,
  clearAllNotifications,
  updateNotificationPriority,
  filterNotificationsByType,
  clearError,
  resetNotifications,
} = notificationSlice.actions;

// Selectors
export const selectNotifications = (state: { notifications: NotificationState }) => 
  state.notifications.notifications;

export const selectUnreadCount = (state: { notifications: NotificationState }) => 
  state.notifications.unreadCount;

export const selectNotificationsLoading = (state: { notifications: NotificationState }) => 
  state.notifications.isLoading;

export const selectNotificationsError = (state: { notifications: NotificationState }) => 
  state.notifications.error;

export const selectHasMoreNotifications = (state: { notifications: NotificationState }) => 
  state.notifications.hasMore;

export const selectNotificationsByType = (state: { notifications: NotificationState }, type: string) =>
  state.notifications.notifications.filter(n => n.type === type);

export const selectUnreadNotifications = (state: { notifications: NotificationState }) =>
  state.notifications.notifications.filter(n => !n.isRead);

export const selectNotificationsByPriority = (state: { notifications: NotificationState }, priority: 'low' | 'medium' | 'high' | 'urgent') =>
  state.notifications.notifications.filter(n => n.priority === priority);

export const selectRecentNotifications = (state: { notifications: NotificationState }, hours = 24) => {
  const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
  return state.notifications.notifications.filter(n => new Date(n.timestamp) > cutoff);
};

export default notificationSlice.reducer;
