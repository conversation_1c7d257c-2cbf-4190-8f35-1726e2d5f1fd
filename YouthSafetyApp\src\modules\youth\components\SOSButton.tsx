import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Vibration,
  Alert,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SOSRequest } from '@types/index';

const { width } = Dimensions.get('window');

interface SOSButtonProps {
  onPress: (emergencyType: string) => void;
  isActive: boolean;
  activeSOS?: SOSRequest | null;
}

const SOSButton: React.FC<SOSButtonProps> = ({ onPress, isActive, activeSOS }) => {
  const [isPressed, setIsPressed] = useState(false);
  const [holdTimer, setHoldTimer] = useState<NodeJS.Timeout | null>(null);
  const [countdown, setCountdown] = useState(5);
  const [showCountdown, setShowCountdown] = useState(false);
  
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const countdownAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isActive) {
      startPulseAnimation();
    } else {
      stopPulseAnimation();
    }
  }, [isActive]);

  useEffect(() => {
    if (showCountdown && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
        Vibration.vibrate(100);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (showCountdown && countdown === 0) {
      handleSOSActivation();
    }
  }, [showCountdown, countdown]);

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const stopPulseAnimation = () => {
    pulseAnim.stopAnimation();
    Animated.timing(pulseAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const handlePressIn = () => {
    setIsPressed(true);
    
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();

    // Start hold timer for panic mode
    const timer = setTimeout(() => {
      startPanicMode();
    }, 1000); // Hold for 1 second to start panic mode
    
    setHoldTimer(timer);
  };

  const handlePressOut = () => {
    setIsPressed(false);
    
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();

    if (holdTimer) {
      clearTimeout(holdTimer);
      setHoldTimer(null);
    }

    if (!showCountdown) {
      // Quick press - show emergency type selection
      showEmergencyTypeSelection();
    }
  };

  const startPanicMode = () => {
    setShowCountdown(true);
    setCountdown(5);
    
    Vibration.vibrate([0, 200, 100, 200]);
    
    Animated.timing(countdownAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const cancelPanicMode = () => {
    setShowCountdown(false);
    setCountdown(5);
    
    Animated.timing(countdownAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const handleSOSActivation = () => {
    setShowCountdown(false);
    onPress('general'); // Default to general emergency for panic mode
  };

  const showEmergencyTypeSelection = () => {
    Alert.alert(
      'Emergency Type',
      'What type of emergency are you experiencing?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Medical Emergency',
          onPress: () => onPress('medical'),
          style: 'destructive',
        },
        {
          text: 'Safety Threat',
          onPress: () => onPress('safety'),
          style: 'destructive',
        },
        {
          text: 'Mental Health Crisis',
          onPress: () => onPress('mental_health'),
        },
        {
          text: 'General Emergency',
          onPress: () => onPress('general'),
          style: 'destructive',
        },
      ]
    );
  };

  if (isActive && activeSOS) {
    return (
      <Animated.View
        style={[
          styles.container,
          { transform: [{ scale: pulseAnim }] }
        ]}
      >
        <View style={[styles.sosButton, styles.activeButton]}>
          <Ionicons name="checkmark-circle" size={48} color="#ffffff" />
          <Text style={styles.activeButtonText}>SOS ACTIVE</Text>
          <Text style={styles.activeSubText}>Help is on the way</Text>
        </View>
      </Animated.View>
    );
  }

  return (
    <View style={styles.container}>
      {showCountdown && (
        <Animated.View
          style={[
            styles.countdownOverlay,
            {
              opacity: countdownAnim,
              transform: [{ scale: countdownAnim }],
            },
          ]}
        >
          <Text style={styles.countdownText}>{countdown}</Text>
          <Text style={styles.countdownLabel}>Activating SOS...</Text>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={cancelPanicMode}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </Animated.View>
      )}
      
      <Animated.View
        style={[
          styles.sosButtonContainer,
          {
            transform: [{ scale: scaleAnim }],
            opacity: showCountdown ? 0.3 : 1,
          },
        ]}
      >
        <TouchableOpacity
          style={[styles.sosButton, isPressed && styles.pressedButton]}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={0.8}
          disabled={showCountdown}
        >
          <Ionicons name="warning" size={48} color="#ffffff" />
          <Text style={styles.sosButtonText}>SOS</Text>
        </TouchableOpacity>
      </Animated.View>
      
      <View style={styles.instructionContainer}>
        <Text style={styles.instructionText}>
          Tap for emergency options
        </Text>
        <Text style={styles.instructionSubText}>
          Hold for 1 second for panic mode
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  sosButtonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  sosButton: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: '#ef4444',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#ef4444',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  pressedButton: {
    backgroundColor: '#dc2626',
  },
  activeButton: {
    backgroundColor: '#10b981',
    shadowColor: '#10b981',
  },
  sosButtonText: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
  },
  activeButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 8,
  },
  activeSubText: {
    color: '#ffffff',
    fontSize: 12,
    marginTop: 4,
  },
  instructionContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  instructionText: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '500',
  },
  instructionSubText: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  countdownOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(239, 68, 68, 0.95)',
    borderRadius: 20,
    zIndex: 10,
    minHeight: 200,
    minWidth: 200,
  },
  countdownText: {
    fontSize: 72,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  countdownLabel: {
    fontSize: 18,
    color: '#ffffff',
    marginTop: 8,
  },
  cancelButton: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 20,
  },
  cancelButtonText: {
    color: '#ef4444',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SOSButton;
