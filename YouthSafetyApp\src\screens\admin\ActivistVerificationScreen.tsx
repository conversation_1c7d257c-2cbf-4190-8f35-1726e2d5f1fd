import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  Image,
  Modal,
  ScrollView,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { adminAPI } from '@/services/api';
import { ActivistUser, Credential } from '@types/index';

interface PendingActivist extends ActivistUser {
  submittedDocuments: {
    id: string;
    type: 'id_card' | 'driving_license' | 'passport' | 'certificate';
    url: string;
    uploadedAt: Date;
  }[];
  verificationNotes?: string;
}

const ActivistVerificationScreen: React.FC = () => {
  const navigation = useNavigation();
  
  const [pendingActivists, setPendingActivists] = useState<PendingActivist[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedActivist, setSelectedActivist] = useState<PendingActivist | null>(null);
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [verificationNotes, setVerificationNotes] = useState('');
  const [verificationAction, setVerificationAction] = useState<'approve' | 'reject' | null>(null);

  useEffect(() => {
    loadPendingVerifications();
  }, []);

  const loadPendingVerifications = async () => {
    try {
      setIsLoading(true);
      const response = await adminAPI.getUsers(1, 50, 'activist');
      const pending = response.data.users.filter(
        (user: ActivistUser) => user.verificationStatus === 'pending'
      );
      setPendingActivists(pending);
    } catch (error) {
      console.error('Error loading pending verifications:', error);
      Alert.alert('Error', 'Failed to load pending verifications');
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewDetails = (activist: PendingActivist) => {
    setSelectedActivist(activist);
    setShowVerificationModal(true);
  };

  const handleVerificationAction = (action: 'approve' | 'reject') => {
    setVerificationAction(action);
    
    if (action === 'reject') {
      Alert.prompt(
        'Rejection Reason',
        'Please provide a reason for rejection:',
        (notes) => {
          if (notes) {
            setVerificationNotes(notes);
            processVerification(action, notes);
          }
        },
        'plain-text',
        '',
        'default'
      );
    } else {
      processVerification(action, 'Approved by admin');
    }
  };

  const processVerification = async (action: 'approve' | 'reject', notes: string) => {
    if (!selectedActivist) return;

    try {
      const status = action === 'approve' ? 'verified' : 'rejected';
      await adminAPI.verifyActivist(selectedActivist.id, status);
      
      // Update local state
      setPendingActivists(prev => 
        prev.filter(activist => activist.id !== selectedActivist.id)
      );
      
      setShowVerificationModal(false);
      setSelectedActivist(null);
      setVerificationNotes('');
      setVerificationAction(null);
      
      Alert.alert(
        'Success',
        `Activist has been ${action === 'approve' ? 'approved' : 'rejected'} successfully`
      );
    } catch (error) {
      console.error('Error processing verification:', error);
      Alert.alert('Error', 'Failed to process verification');
    }
  };

  const getDocumentTypeIcon = (type: string) => {
    switch (type) {
      case 'id_card': return 'card';
      case 'driving_license': return 'car';
      case 'passport': return 'airplane';
      case 'certificate': return 'ribbon';
      default: return 'document';
    }
  };

  const getDocumentTypeName = (type: string) => {
    switch (type) {
      case 'id_card': return 'ID Card';
      case 'driving_license': return 'Driving License';
      case 'passport': return 'Passport';
      case 'certificate': return 'Certificate';
      default: return 'Document';
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const renderActivistItem = ({ item }: { item: PendingActivist }) => (
    <TouchableOpacity
      style={styles.activistCard}
      onPress={() => handleViewDetails(item)}
    >
      <View style={styles.activistHeader}>
        <View style={styles.activistInfo}>
          {item.avatar ? (
            <Image source={{ uri: item.avatar }} style={styles.avatar} />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <Ionicons name="person" size={24} color="#6b7280" />
            </View>
          )}
          <View style={styles.activistDetails}>
            <Text style={styles.activistName}>
              {item.firstName} {item.lastName}
            </Text>
            <Text style={styles.activistEmail}>{item.email}</Text>
            <Text style={styles.activistPhone}>{item.phone}</Text>
          </View>
        </View>
        <View style={styles.statusBadge}>
          <Text style={styles.statusText}>PENDING</Text>
        </View>
      </View>

      <View style={styles.activistMeta}>
        <View style={styles.metaItem}>
          <Ionicons name="calendar" size={16} color="#6b7280" />
          <Text style={styles.metaText}>
            Applied: {formatDate(item.createdAt)}
          </Text>
        </View>
        <View style={styles.metaItem}>
          <Ionicons name="document" size={16} color="#6b7280" />
          <Text style={styles.metaText}>
            {item.submittedDocuments?.length || 0} documents
          </Text>
        </View>
      </View>

      <View style={styles.specializationsContainer}>
        <Text style={styles.specializationsLabel}>Specializations:</Text>
        <View style={styles.specializationTags}>
          {item.specializations.slice(0, 3).map((spec, index) => (
            <View key={index} style={styles.specializationTag}>
              <Text style={styles.specializationText}>{spec}</Text>
            </View>
          ))}
          {item.specializations.length > 3 && (
            <View style={styles.specializationTag}>
              <Text style={styles.specializationText}>
                +{item.specializations.length - 3} more
              </Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="#1f2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Activist Verifications</Text>
        <View style={styles.pendingBadge}>
          <Text style={styles.pendingBadgeText}>{pendingActivists.length}</Text>
        </View>
      </View>

      {/* Pending List */}
      <FlatList
        data={pendingActivists}
        renderItem={renderActivistItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshing={isLoading}
        onRefresh={loadPendingVerifications}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="checkmark-circle" size={64} color="#10b981" />
            <Text style={styles.emptyTitle}>All Caught Up!</Text>
            <Text style={styles.emptySubtitle}>
              No pending activist verifications at the moment.
            </Text>
          </View>
        }
      />

      {/* Verification Modal */}
      <Modal
        visible={showVerificationModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          {selectedActivist && (
            <>
              {/* Modal Header */}
              <View style={styles.modalHeader}>
                <TouchableOpacity onPress={() => setShowVerificationModal(false)}>
                  <Text style={styles.cancelButton}>Cancel</Text>
                </TouchableOpacity>
                <Text style={styles.modalTitle}>Verify Activist</Text>
                <View style={styles.placeholder} />
              </View>

              <ScrollView style={styles.modalContent}>
                {/* Activist Info */}
                <View style={styles.activistSection}>
                  <Text style={styles.sectionTitle}>Activist Information</Text>
                  <View style={styles.infoCard}>
                    <View style={styles.infoRow}>
                      <Text style={styles.infoLabel}>Name:</Text>
                      <Text style={styles.infoValue}>
                        {selectedActivist.firstName} {selectedActivist.lastName}
                      </Text>
                    </View>
                    <View style={styles.infoRow}>
                      <Text style={styles.infoLabel}>Email:</Text>
                      <Text style={styles.infoValue}>{selectedActivist.email}</Text>
                    </View>
                    <View style={styles.infoRow}>
                      <Text style={styles.infoLabel}>Phone:</Text>
                      <Text style={styles.infoValue}>{selectedActivist.phone}</Text>
                    </View>
                    <View style={styles.infoRow}>
                      <Text style={styles.infoLabel}>Service Area:</Text>
                      <Text style={styles.infoValue}>
                        {selectedActivist.serviceArea?.city}, {selectedActivist.serviceArea?.state}
                      </Text>
                    </View>
                  </View>
                </View>

                {/* Specializations */}
                <View style={styles.activistSection}>
                  <Text style={styles.sectionTitle}>Specializations</Text>
                  <View style={styles.specializationsList}>
                    {selectedActivist.specializations.map((spec, index) => (
                      <View key={index} style={styles.specializationItem}>
                        <Ionicons name="checkmark-circle" size={16} color="#10b981" />
                        <Text style={styles.specializationItemText}>{spec}</Text>
                      </View>
                    ))}
                  </View>
                </View>

                {/* Credentials */}
                <View style={styles.activistSection}>
                  <Text style={styles.sectionTitle}>Credentials</Text>
                  {selectedActivist.credentials.map((credential, index) => (
                    <View key={index} style={styles.credentialCard}>
                      <View style={styles.credentialHeader}>
                        <Text style={styles.credentialType}>{credential.type}</Text>
                        <Text style={styles.credentialIssuer}>{credential.issuer}</Text>
                      </View>
                      <Text style={styles.credentialNumber}>#{credential.number}</Text>
                      <Text style={styles.credentialExpiry}>
                        Expires: {formatDate(credential.expiryDate)}
                      </Text>
                    </View>
                  ))}
                </View>

                {/* Submitted Documents */}
                <View style={styles.activistSection}>
                  <Text style={styles.sectionTitle}>Submitted Documents</Text>
                  {selectedActivist.submittedDocuments?.map((doc, index) => (
                    <View key={index} style={styles.documentCard}>
                      <View style={styles.documentHeader}>
                        <Ionicons 
                          name={getDocumentTypeIcon(doc.type) as any} 
                          size={20} 
                          color="#6366f1" 
                        />
                        <Text style={styles.documentType}>
                          {getDocumentTypeName(doc.type)}
                        </Text>
                      </View>
                      <Text style={styles.documentDate}>
                        Uploaded: {formatDate(doc.uploadedAt)}
                      </Text>
                      <TouchableOpacity style={styles.viewDocumentButton}>
                        <Text style={styles.viewDocumentText}>View Document</Text>
                        <Ionicons name="eye" size={16} color="#6366f1" />
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              </ScrollView>

              {/* Action Buttons */}
              <View style={styles.actionContainer}>
                <TouchableOpacity
                  style={styles.rejectButton}
                  onPress={() => handleVerificationAction('reject')}
                >
                  <Ionicons name="close-circle" size={20} color="#ffffff" />
                  <Text style={styles.actionButtonText}>Reject</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.approveButton}
                  onPress={() => handleVerificationAction('approve')}
                >
                  <Ionicons name="checkmark-circle" size={20} color="#ffffff" />
                  <Text style={styles.actionButtonText}>Approve</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  pendingBadge: {
    backgroundColor: '#f59e0b',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 24,
    alignItems: 'center',
  },
  pendingBadgeText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
  listContainer: {
    padding: 16,
  },
  activistCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  activistHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  activistInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  avatarPlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  activistDetails: {
    flex: 1,
  },
  activistName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  activistEmail: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  activistPhone: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  statusBadge: {
    backgroundColor: '#fef3c7',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#92400e',
  },
  activistMeta: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 12,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaText: {
    fontSize: 12,
    color: '#6b7280',
    marginLeft: 4,
  },
  specializationsContainer: {
    marginTop: 8,
  },
  specializationsLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  specializationTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  specializationTag: {
    backgroundColor: '#e0e7ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  specializationText: {
    fontSize: 12,
    color: '#6366f1',
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginTop: 16,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 24,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  cancelButton: {
    fontSize: 16,
    color: '#6b7280',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  placeholder: {
    width: 60,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  activistSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  infoCard: {
    backgroundColor: '#f8fafc',
    borderRadius: 8,
    padding: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  infoLabel: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 14,
    color: '#1f2937',
    flex: 1,
    textAlign: 'right',
  },
  specializationsList: {
    gap: 8,
  },
  specializationItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  specializationItemText: {
    fontSize: 14,
    color: '#374151',
    marginLeft: 8,
  },
  credentialCard: {
    backgroundColor: '#f8fafc',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  credentialHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  credentialType: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
  },
  credentialIssuer: {
    fontSize: 12,
    color: '#6b7280',
  },
  credentialNumber: {
    fontSize: 12,
    color: '#374151',
    marginBottom: 4,
  },
  credentialExpiry: {
    fontSize: 12,
    color: '#6b7280',
  },
  documentCard: {
    backgroundColor: '#f8fafc',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  documentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  documentType: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
    marginLeft: 8,
  },
  documentDate: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 8,
  },
  viewDocumentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#e0e7ff',
    paddingVertical: 8,
    borderRadius: 6,
  },
  viewDocumentText: {
    fontSize: 14,
    color: '#6366f1',
    fontWeight: '500',
    marginRight: 4,
  },
  actionContainer: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  rejectButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ef4444',
    paddingVertical: 16,
    borderRadius: 8,
    gap: 8,
  },
  approveButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#10b981',
    paddingVertical: 16,
    borderRadius: 8,
    gap: 8,
  },
  actionButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ActivistVerificationScreen;
