import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { privacyService, PrivacySettings, DataRetentionSettings } from '@/services/privacyService';

const PrivacySettingsScreen: React.FC = () => {
  const navigation = useNavigation();
  
  const [privacySettings, setPrivacySettings] = useState<PrivacySettings | null>(null);
  const [dataRetentionSettings, setDataRetentionSettings] = useState<DataRetentionSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showDataExportModal, setShowDataExportModal] = useState(false);
  const [showDeleteAccountModal, setShowDeleteAccountModal] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      const privacy = await privacyService.loadPrivacySettings();
      const retention = await privacyService.loadDataRetentionSettings();
      setPrivacySettings(privacy);
      setDataRetentionSettings(retention);
    } catch (error) {
      console.error('Error loading settings:', error);
      Alert.alert('Error', 'Failed to load privacy settings');
    } finally {
      setIsLoading(false);
    }
  };

  const updatePrivacySetting = async (key: keyof PrivacySettings, value: any) => {
    try {
      await privacyService.updatePrivacySettings({ [key]: value });
      setPrivacySettings(prev => prev ? { ...prev, [key]: value } : null);
    } catch (error) {
      console.error('Error updating privacy setting:', error);
      Alert.alert('Error', 'Failed to update privacy setting');
    }
  };

  const updateRetentionSetting = async (key: keyof DataRetentionSettings, value: number) => {
    try {
      await privacyService.updateDataRetentionSettings({ [key]: value });
      setDataRetentionSettings(prev => prev ? { ...prev, [key]: value } : null);
    } catch (error) {
      console.error('Error updating retention setting:', error);
      Alert.alert('Error', 'Failed to update retention setting');
    }
  };

  const handleDataExport = async () => {
    try {
      setShowDataExportModal(false);
      Alert.alert(
        'Data Export',
        'Your data export has been initiated. You will receive an email with your data within 24 hours.',
        [{ text: 'OK' }]
      );
      
      // In a real implementation, this would trigger the export process
      await privacyService.exportUserData();
    } catch (error) {
      console.error('Error exporting data:', error);
      Alert.alert('Error', 'Failed to export data');
    }
  };

  const handleDeleteAccount = async () => {
    try {
      setShowDeleteAccountModal(false);
      
      Alert.alert(
        'Account Deleted',
        'Your account and all associated data have been permanently deleted.',
        [
          {
            text: 'OK',
            onPress: () => {
              // Navigate to login screen
              navigation.reset({
                index: 0,
                routes: [{ name: 'Login' as never }],
              });
            },
          },
        ]
      );
      
      await privacyService.deleteAllUserData();
    } catch (error) {
      console.error('Error deleting account:', error);
      Alert.alert('Error', 'Failed to delete account');
    }
  };

  const getRetentionOptions = () => [
    { label: '7 days', value: 7 },
    { label: '30 days', value: 30 },
    { label: '90 days', value: 90 },
    { label: '1 year', value: 365 },
    { label: 'Never delete', value: -1 },
  ];

  const showRetentionPicker = (key: keyof DataRetentionSettings, currentValue: number) => {
    const options = getRetentionOptions();
    const currentLabel = options.find(opt => opt.value === currentValue)?.label || `${currentValue} days`;
    
    Alert.alert(
      'Data Retention Period',
      `Current setting: ${currentLabel}`,
      [
        { text: 'Cancel', style: 'cancel' },
        ...options.map(option => ({
          text: option.label,
          onPress: () => updateRetentionSetting(key, option.value),
        })),
      ]
    );
  };

  if (isLoading || !privacySettings || !dataRetentionSettings) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading privacy settings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="#1f2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Privacy & Security</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        {/* Privacy Controls */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy Controls</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Profile Visibility</Text>
              <Text style={styles.settingDescription}>
                Who can see your profile information
              </Text>
            </View>
            <TouchableOpacity
              style={styles.valueButton}
              onPress={() => {
                Alert.alert(
                  'Profile Visibility',
                  'Choose who can see your profile',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    { text: 'Public', onPress: () => updatePrivacySetting('profileVisibility', 'public') },
                    { text: 'Friends Only', onPress: () => updatePrivacySetting('profileVisibility', 'friends') },
                    { text: 'Private', onPress: () => updatePrivacySetting('profileVisibility', 'private') },
                  ]
                );
              }}
            >
              <Text style={styles.valueText}>{privacySettings.profileVisibility}</Text>
              <Ionicons name="chevron-forward" size={16} color="#6b7280" />
            </TouchableOpacity>
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Location Sharing</Text>
              <Text style={styles.settingDescription}>
                When to share your location
              </Text>
            </View>
            <TouchableOpacity
              style={styles.valueButton}
              onPress={() => {
                Alert.alert(
                  'Location Sharing',
                  'Choose when to share your location',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    { text: 'Always', onPress: () => updatePrivacySetting('locationSharing', 'always') },
                    { text: 'Emergency Only', onPress: () => updatePrivacySetting('locationSharing', 'emergency_only') },
                    { text: 'Never', onPress: () => updatePrivacySetting('locationSharing', 'never') },
                  ]
                );
              }}
            >
              <Text style={styles.valueText}>{privacySettings.locationSharing.replace('_', ' ')}</Text>
              <Ionicons name="chevron-forward" size={16} color="#6b7280" />
            </TouchableOpacity>
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Anonymous Posting</Text>
              <Text style={styles.settingDescription}>
                Post without revealing your identity
              </Text>
            </View>
            <Switch
              value={privacySettings.anonymousPosting}
              onValueChange={(value) => updatePrivacySetting('anonymousPosting', value)}
              trackColor={{ false: '#e5e7eb', true: '#6366f1' }}
              thumbColor={privacySettings.anonymousPosting ? '#ffffff' : '#f3f4f6'}
            />
          </View>
        </View>

        {/* Data Collection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Collection</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Data Collection</Text>
              <Text style={styles.settingDescription}>
                Allow collection of usage data for app improvement
              </Text>
            </View>
            <Switch
              value={privacySettings.dataCollection}
              onValueChange={(value) => updatePrivacySetting('dataCollection', value)}
              trackColor={{ false: '#e5e7eb', true: '#6366f1' }}
              thumbColor={privacySettings.dataCollection ? '#ffffff' : '#f3f4f6'}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Analytics Opt-out</Text>
              <Text style={styles.settingDescription}>
                Opt out of analytics tracking
              </Text>
            </View>
            <Switch
              value={privacySettings.analyticsOptOut}
              onValueChange={(value) => updatePrivacySetting('analyticsOptOut', value)}
              trackColor={{ false: '#e5e7eb', true: '#6366f1' }}
              thumbColor={privacySettings.analyticsOptOut ? '#ffffff' : '#f3f4f6'}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Marketing Opt-out</Text>
              <Text style={styles.settingDescription}>
                Opt out of marketing communications
              </Text>
            </View>
            <Switch
              value={privacySettings.marketingOptOut}
              onValueChange={(value) => updatePrivacySetting('marketingOptOut', value)}
              trackColor={{ false: '#e5e7eb', true: '#6366f1' }}
              thumbColor={privacySettings.marketingOptOut ? '#ffffff' : '#f3f4f6'}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Third-party Sharing</Text>
              <Text style={styles.settingDescription}>
                Allow sharing data with trusted partners
              </Text>
            </View>
            <Switch
              value={privacySettings.thirdPartySharing}
              onValueChange={(value) => updatePrivacySetting('thirdPartySharing', value)}
              trackColor={{ false: '#e5e7eb', true: '#6366f1' }}
              thumbColor={privacySettings.thirdPartySharing ? '#ffffff' : '#f3f4f6'}
            />
          </View>
        </View>

        {/* Data Retention */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Retention</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Chat History</Text>
              <Text style={styles.settingDescription}>
                How long to keep chat messages
              </Text>
            </View>
            <TouchableOpacity
              style={styles.valueButton}
              onPress={() => showRetentionPicker('chatHistory', dataRetentionSettings.chatHistory)}
            >
              <Text style={styles.valueText}>
                {dataRetentionSettings.chatHistory === -1 ? 'Never delete' : `${dataRetentionSettings.chatHistory} days`}
              </Text>
              <Ionicons name="chevron-forward" size={16} color="#6b7280" />
            </TouchableOpacity>
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Location History</Text>
              <Text style={styles.settingDescription}>
                How long to keep location data
              </Text>
            </View>
            <TouchableOpacity
              style={styles.valueButton}
              onPress={() => showRetentionPicker('locationHistory', dataRetentionSettings.locationHistory)}
            >
              <Text style={styles.valueText}>
                {dataRetentionSettings.locationHistory === -1 ? 'Never delete' : `${dataRetentionSettings.locationHistory} days`}
              </Text>
              <Ionicons name="chevron-forward" size={16} color="#6b7280" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Data Rights */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Your Data Rights</Text>
          
          <TouchableOpacity
            style={styles.actionItem}
            onPress={() => setShowDataExportModal(true)}
          >
            <Ionicons name="download-outline" size={20} color="#6366f1" />
            <Text style={styles.actionText}>Export My Data</Text>
            <Ionicons name="chevron-forward" size={16} color="#6b7280" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionItem}
            onPress={() => navigation.navigate('ConsentHistory' as never)}
          >
            <Ionicons name="document-text-outline" size={20} color="#6366f1" />
            <Text style={styles.actionText}>Consent History</Text>
            <Ionicons name="chevron-forward" size={16} color="#6b7280" />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionItem, styles.dangerAction]}
            onPress={() => setShowDeleteAccountModal(true)}
          >
            <Ionicons name="trash-outline" size={20} color="#ef4444" />
            <Text style={[styles.actionText, styles.dangerText]}>Delete My Account</Text>
            <Ionicons name="chevron-forward" size={16} color="#ef4444" />
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Data Export Modal */}
      <Modal
        visible={showDataExportModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowDataExportModal(false)}>
              <Text style={styles.cancelButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Export Data</Text>
            <TouchableOpacity onPress={handleDataExport}>
              <Text style={styles.confirmButton}>Export</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.modalContent}>
            <Text style={styles.modalDescription}>
              We'll prepare a complete copy of your data including:
            </Text>
            <View style={styles.dataList}>
              <Text style={styles.dataItem}>• Profile information</Text>
              <Text style={styles.dataItem}>• Posts and comments</Text>
              <Text style={styles.dataItem}>• Messages and conversations</Text>
              <Text style={styles.dataItem}>• SOS history</Text>
              <Text style={styles.dataItem}>• Privacy settings</Text>
              <Text style={styles.dataItem}>• Consent history</Text>
            </View>
            <Text style={styles.modalNote}>
              You'll receive an email with a secure download link within 24 hours.
            </Text>
          </View>
        </SafeAreaView>
      </Modal>

      {/* Delete Account Modal */}
      <Modal
        visible={showDeleteAccountModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowDeleteAccountModal(false)}>
              <Text style={styles.cancelButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Delete Account</Text>
            <TouchableOpacity onPress={handleDeleteAccount}>
              <Text style={[styles.confirmButton, styles.dangerText]}>Delete</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.modalContent}>
            <View style={styles.warningContainer}>
              <Ionicons name="warning" size={48} color="#ef4444" />
              <Text style={styles.warningTitle}>This action cannot be undone</Text>
            </View>
            <Text style={styles.modalDescription}>
              Deleting your account will permanently remove:
            </Text>
            <View style={styles.dataList}>
              <Text style={styles.dataItem}>• Your profile and all personal information</Text>
              <Text style={styles.dataItem}>• All posts, comments, and messages</Text>
              <Text style={styles.dataItem}>• SOS history and emergency contacts</Text>
              <Text style={styles.dataItem}>• All app data and preferences</Text>
            </View>
            <Text style={styles.modalNote}>
              This action is permanent and cannot be reversed. Make sure to export your data first if you want to keep a copy.
            </Text>
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  placeholder: {
    width: 24,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#ffffff',
    marginTop: 16,
    paddingVertical: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#f8fafc',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
  },
  settingDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  valueButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  valueText: {
    fontSize: 16,
    color: '#6366f1',
    marginRight: 8,
    textTransform: 'capitalize',
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  actionText: {
    fontSize: 16,
    color: '#1f2937',
    marginLeft: 12,
    flex: 1,
  },
  dangerAction: {
    backgroundColor: '#fef2f2',
  },
  dangerText: {
    color: '#ef4444',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  cancelButton: {
    fontSize: 16,
    color: '#6b7280',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  confirmButton: {
    fontSize: 16,
    color: '#6366f1',
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  modalDescription: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
    marginBottom: 16,
  },
  dataList: {
    marginBottom: 16,
  },
  dataItem: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
    marginBottom: 4,
  },
  modalNote: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
    fontStyle: 'italic',
  },
  warningContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  warningTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ef4444',
    marginTop: 12,
  },
});

export default PrivacySettingsScreen;
