import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { logoutUser } from '../../store/slices/authSlice';

const YouthDashboardScreen: React.FC = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch<AppDispatch>();

  const handleQuickAction = (action: string) => {
    Alert.alert('Feature Coming Soon', `${action} feature will be available soon!`);
  };

  const handleSOSPress = () => {
    Alert.alert(
      'Emergency SOS',
      'This would activate the emergency SOS system with location tracking and contact notifications.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Activate SOS', style: 'destructive', onPress: () => {
          Alert.alert('SOS Activated', 'Emergency services and contacts have been notified.');
        }},
      ]
    );
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: () => {
          dispatch(logoutUser());
        }},
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>Hello, {user?.firstName}!</Text>
            <Text style={styles.subtitle}>Stay safe and connected</Text>
          </View>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Text style={styles.logoutText}>Logout</Text>
          </TouchableOpacity>
        </View>

        {/* Emergency SOS Section */}
        <View style={styles.emergencySection}>
          <Text style={styles.sectionTitle}>Emergency</Text>
          <TouchableOpacity style={styles.sosButton} onPress={handleSOSPress}>
            <View style={styles.sosButtonInner}>
              <Text style={styles.sosText}>🆘</Text>
              <Text style={styles.sosLabel}>Emergency SOS</Text>
              <Text style={styles.sosSubtext}>Tap to activate</Text>
            </View>
          </TouchableOpacity>
          <Text style={styles.emergencyNote}>
            Tap the SOS button in case of emergency. Your location will be shared with nearby activists.
          </Text>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity 
              style={styles.quickActionCard}
              onPress={() => handleQuickAction('Anonymous Confession')}
            >
              <Text style={styles.actionIcon}>💬</Text>
              <Text style={styles.quickActionText}>Anonymous Confession</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.quickActionCard}
              onPress={() => handleQuickAction('Find Events')}
            >
              <Text style={styles.actionIcon}>📅</Text>
              <Text style={styles.quickActionText}>Find Events</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.quickActionCard}
              onPress={() => handleQuickAction('Safety Tips')}
            >
              <Text style={styles.actionIcon}>🛡️</Text>
              <Text style={styles.quickActionText}>Safety Tips</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.quickActionCard}
              onPress={() => handleQuickAction('Support Chat')}
            >
              <Text style={styles.actionIcon}>🆘</Text>
              <Text style={styles.quickActionText}>Support Chat</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.recentActivitySection}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          <View style={styles.activityCard}>
            <Text style={styles.timeIcon}>⏰</Text>
            <Text style={styles.activityText}>No recent activity</Text>
          </View>
        </View>

        {/* Safety Status */}
        <View style={styles.safetyStatusSection}>
          <Text style={styles.sectionTitle}>Safety Status</Text>
          <View style={styles.safetyStatusCard}>
            <View style={styles.safetyIndicator}>
              <View style={[styles.statusDot, styles.statusSafe]} />
              <Text style={styles.safetyStatusText}>You're Safe</Text>
            </View>
            <Text style={styles.safetyStatusSubtext}>
              Last check-in: Today at 2:30 PM
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContent: {
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 30,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 2,
  },
  logoutButton: {
    backgroundColor: '#e74c3c',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  logoutText: {
    color: '#fff',
    fontWeight: '600',
  },
  emergencySection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  emergencyNote: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 10,
    lineHeight: 20,
  },
  sosButton: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#e74c3c',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  sosButtonInner: {
    alignItems: 'center',
  },
  sosText: {
    fontSize: 32,
    marginBottom: 4,
  },
  sosLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 2,
  },
  sosSubtext: {
    fontSize: 10,
    color: '#fff',
    opacity: 0.9,
  },
  actionIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  timeIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  quickActionsSection: {
    marginBottom: 20,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionCard: {
    width: '48%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    marginTop: 10,
  },
  recentActivitySection: {
    marginBottom: 20,
  },
  activityCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  activityText: {
    fontSize: 16,
    color: '#666',
    marginLeft: 10,
  },
  safetyStatusSection: {
    marginBottom: 20,
  },
  safetyStatusCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  safetyIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 10,
  },
  statusSafe: {
    backgroundColor: '#28a745',
  },
  safetyStatusText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  safetyStatusSubtext: {
    fontSize: 14,
    color: '#666',
    marginLeft: 22,
  },
});

export default YouthDashboardScreen;
