/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 * @format
 */

import invariant from 'fbjs/lib/invariant';
import * as React from 'react';

/**
 * `setState` is called asynchronously, and should not rely on the value of
 * `this.props` or `this.state`:
 * https://reactjs.org/docs/state-and-lifecycle.html#state-updates-may-be-asynchronous
 *
 * SafePureComponent adds runtime enforcement, to catch cases where these
 * variables are read in a state updater function, instead of the ones passed
 * in.
 */
declare export default class StateSafePureComponent<Props, State: interface {}> extends React.PureComponent<Props, State> {
  _inAsyncStateUpdate: any,
  constructor(props: Props): any,
  setState(partialState: ?($Shape<State> | ((State, Props) => ?$Shape<State>)), callback?: () => mixed): void,
  _installSetStateHooks(): any,
}