import CryptoJ<PERSON> from 'crypto-js';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

export class EncryptionService {
  private static instance: EncryptionService;
  private encryptionKey: string | null = null;

  static getInstance(): EncryptionService {
    if (!EncryptionService.instance) {
      EncryptionService.instance = new EncryptionService();
    }
    return EncryptionService.instance;
  }

  async initialize(): Promise<void> {
    try {
      // Try to get existing encryption key
      this.encryptionKey = await SecureStore.getItemAsync('encryption_key');
      
      if (!this.encryptionKey) {
        // Generate new encryption key
        this.encryptionKey = this.generateEncryptionKey();
        await SecureStore.setItemAsync('encryption_key', this.encryptionKey);
      }
    } catch (error) {
      console.error('Error initializing encryption service:', error);
      // Fallback to session-only encryption
      this.encryptionKey = this.generateEncryptionKey();
    }
  }

  private generateEncryptionKey(): string {
    return CryptoJS.lib.WordArray.random(256/8).toString();
  }

  // Encrypt sensitive data
  encrypt(data: string): string {
    if (!this.encryptionKey) {
      throw new Error('Encryption service not initialized');
    }

    try {
      const encrypted = CryptoJS.AES.encrypt(data, this.encryptionKey).toString();
      return encrypted;
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  // Decrypt sensitive data
  decrypt(encryptedData: string): string {
    if (!this.encryptionKey) {
      throw new Error('Encryption service not initialized');
    }

    try {
      const decrypted = CryptoJS.AES.decrypt(encryptedData, this.encryptionKey);
      return decrypted.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  // Hash sensitive data (one-way)
  hash(data: string): string {
    return CryptoJS.SHA256(data).toString();
  }

  // Generate secure random token
  generateSecureToken(length: number = 32): string {
    return CryptoJS.lib.WordArray.random(length).toString();
  }

  // Encrypt medical information
  encryptMedicalInfo(medicalInfo: any): string {
    const jsonString = JSON.stringify(medicalInfo);
    return this.encrypt(jsonString);
  }

  // Decrypt medical information
  decryptMedicalInfo(encryptedMedicalInfo: string): any {
    const decryptedString = this.decrypt(encryptedMedicalInfo);
    return JSON.parse(decryptedString);
  }

  // Encrypt emergency contacts
  encryptEmergencyContacts(contacts: any[]): string {
    const jsonString = JSON.stringify(contacts);
    return this.encrypt(jsonString);
  }

  // Decrypt emergency contacts
  decryptEmergencyContacts(encryptedContacts: string): any[] {
    const decryptedString = this.decrypt(encryptedContacts);
    return JSON.parse(decryptedString);
  }

  // Secure storage for sensitive data
  async secureStore(key: string, value: string): Promise<void> {
    try {
      const encryptedValue = this.encrypt(value);
      await SecureStore.setItemAsync(key, encryptedValue);
    } catch (error) {
      console.error('Secure store error:', error);
      throw new Error('Failed to securely store data');
    }
  }

  // Secure retrieval of sensitive data
  async secureRetrieve(key: string): Promise<string | null> {
    try {
      const encryptedValue = await SecureStore.getItemAsync(key);
      if (!encryptedValue) return null;
      
      return this.decrypt(encryptedValue);
    } catch (error) {
      console.error('Secure retrieve error:', error);
      return null;
    }
  }

  // Remove securely stored data
  async secureRemove(key: string): Promise<void> {
    try {
      await SecureStore.deleteItemAsync(key);
    } catch (error) {
      console.error('Secure remove error:', error);
    }
  }

  // Generate end-to-end encryption keys for messaging
  generateE2EKeys(): { publicKey: string; privateKey: string } {
    // In a real implementation, you would use proper asymmetric encryption
    // For this example, we'll use a simplified approach
    const keyPair = {
      publicKey: this.generateSecureToken(64),
      privateKey: this.generateSecureToken(64),
    };
    
    return keyPair;
  }

  // Encrypt message for end-to-end encryption
  encryptMessage(message: string, recipientPublicKey: string): string {
    // Simplified E2E encryption - in production, use proper asymmetric encryption
    const combinedKey = this.hash(recipientPublicKey + this.encryptionKey);
    return CryptoJS.AES.encrypt(message, combinedKey).toString();
  }

  // Decrypt message for end-to-end encryption
  decryptMessage(encryptedMessage: string, senderPublicKey: string): string {
    // Simplified E2E decryption - in production, use proper asymmetric encryption
    const combinedKey = this.hash(senderPublicKey + this.encryptionKey);
    const decrypted = CryptoJS.AES.decrypt(encryptedMessage, combinedKey);
    return decrypted.toString(CryptoJS.enc.Utf8);
  }

  // Validate data integrity
  createChecksum(data: string): string {
    return CryptoJS.SHA256(data).toString();
  }

  // Verify data integrity
  verifyChecksum(data: string, checksum: string): boolean {
    const calculatedChecksum = this.createChecksum(data);
    return calculatedChecksum === checksum;
  }

  // Secure random number generation
  generateSecureRandom(min: number, max: number): number {
    const range = max - min + 1;
    const randomBytes = CryptoJS.lib.WordArray.random(4);
    const randomValue = parseInt(randomBytes.toString().substring(0, 8), 16);
    return min + (randomValue % range);
  }

  // Clear encryption key (for logout)
  clearEncryptionKey(): void {
    this.encryptionKey = null;
  }

  // Re-encrypt data with new key (for key rotation)
  async rotateEncryptionKey(): Promise<void> {
    if (!this.encryptionKey) {
      throw new Error('No existing encryption key to rotate');
    }

    const oldKey = this.encryptionKey;
    const newKey = this.generateEncryptionKey();

    try {
      // Store new key
      await SecureStore.setItemAsync('encryption_key', newKey);
      this.encryptionKey = newKey;

      // In a real implementation, you would re-encrypt all stored data
      // with the new key here
      
    } catch (error) {
      // Rollback on error
      this.encryptionKey = oldKey;
      throw new Error('Failed to rotate encryption key');
    }
  }

  // Sanitize sensitive data for logging
  sanitizeForLogging(data: any): any {
    const sensitiveFields = [
      'password',
      'token',
      'key',
      'secret',
      'phone',
      'email',
      'ssn',
      'medical',
      'emergency',
    ];

    const sanitized = JSON.parse(JSON.stringify(data));

    const sanitizeObject = (obj: any): any => {
      if (typeof obj !== 'object' || obj === null) {
        return obj;
      }

      if (Array.isArray(obj)) {
        return obj.map(sanitizeObject);
      }

      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        const lowerKey = key.toLowerCase();
        const isSensitive = sensitiveFields.some(field => lowerKey.includes(field));
        
        if (isSensitive) {
          result[key] = '[REDACTED]';
        } else if (typeof value === 'object') {
          result[key] = sanitizeObject(value);
        } else {
          result[key] = value;
        }
      }
      return result;
    };

    return sanitizeObject(sanitized);
  }
}

export const encryptionService = EncryptionService.getInstance();
