import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('youth_profiles', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.integer('age').notNullable();
    table.boolean('parental_consent').defaultTo(false);
    table.json('emergency_contacts').defaultTo('[]');
    table.json('medical_info').defaultTo('{}');
    table.json('preferences').defaultTo('{}');
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['age']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('youth_profiles');
}
