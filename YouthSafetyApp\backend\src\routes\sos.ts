import express from 'express';
import <PERSON><PERSON> from 'joi';
import { db } from '../database/connection';
import { AuthenticatedRequest, requireRole } from '../middleware/auth';
import { logger } from '../utils/logger';
import { findNearestActivists } from '../services/location';
import { sendSOSNotification } from '../services/notification';
import { io } from '../server';

const router = express.Router();

// Validation schemas
const createSOSSchema = Joi.object({
  latitude: Joi.number().min(-90).max(90).required(),
  longitude: Joi.number().min(-180).max(180).required(),
  accuracy: Joi.number().min(0),
  address: Joi.string().max(500),
  emergencyType: Joi.string().valid('medical', 'safety', 'mental_health', 'general').required(),
  description: Joi.string().max(1000),
  priority: Joi.string().valid('low', 'medium', 'high', 'critical').default('medium')
});

// Create SOS request
router.post('/', requireRole(['youth']), async (req: AuthenticatedRequest, res) => {
  try {
    const { error, value } = createSOSSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const userId = req.user!.id;
    const { latitude, longitude, accuracy, address, emergencyType, description, priority } = value;

    // Check if user already has an active SOS
    const existingActiveSOS = await db('sos_requests')
      .where({ user_id: userId, status: 'active' })
      .first();

    if (existingActiveSOS) {
      return res.status(400).json({
        success: false,
        message: 'You already have an active SOS request'
      });
    }

    // Get user's medical info
    const youthProfile = await db('youth_profiles')
      .where({ user_id: userId })
      .first();

    // Create SOS request
    const [sosRequest] = await db('sos_requests')
      .insert({
        user_id: userId,
        latitude,
        longitude,
        accuracy,
        address,
        emergency_type: emergencyType,
        description,
        priority,
        medical_info: youthProfile?.medical_info || {},
        status: 'active'
      })
      .returning('*');

    // Find nearest activists
    const nearestActivists = await findNearestActivists(latitude, longitude, 10); // 10km radius

    if (nearestActivists.length === 0) {
      logger.warn(`No activists found near SOS request ${sosRequest.id}`);
      // Could implement fallback to emergency services here
    } else {
      // Notify nearest activists
      for (const activist of nearestActivists.slice(0, 3)) { // Notify top 3 nearest
        await sendSOSNotification(activist.user_id, sosRequest);
        
        // Send real-time notification via socket
        io.to(`user_${activist.user_id}`).emit('new_sos_request', {
          sosRequest,
          distance: activist.distance
        });
      }
    }

    // Notify emergency contacts
    if (youthProfile?.emergency_contacts) {
      const emergencyContacts = JSON.parse(youthProfile.emergency_contacts);
      for (const contact of emergencyContacts) {
        // Send SMS to emergency contacts (implement based on your SMS service)
        // await sendEmergencySMS(contact.phone, sosRequest);
      }
    }

    res.status(201).json({
      success: true,
      message: 'SOS request created successfully',
      data: { sosRequest }
    });

  } catch (error) {
    logger.error('Create SOS error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get user's SOS requests
router.get('/my-requests', async (req: AuthenticatedRequest, res) => {
  try {
    const userId = req.user!.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    const sosRequests = await db('sos_requests')
      .where({ user_id: userId })
      .orderBy('created_at', 'desc')
      .limit(limit)
      .offset(offset);

    const total = await db('sos_requests')
      .where({ user_id: userId })
      .count('* as count')
      .first();

    res.json({
      success: true,
      data: {
        sosRequests,
        pagination: {
          page,
          limit,
          total: parseInt(total?.count as string) || 0,
          pages: Math.ceil((parseInt(total?.count as string) || 0) / limit)
        }
      }
    });

  } catch (error) {
    logger.error('Get SOS requests error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Accept SOS request (Activist only)
router.post('/:id/accept', requireRole(['activist']), async (req: AuthenticatedRequest, res) => {
  try {
    const sosId = req.params.id;
    const activistId = req.user!.id;

    // Check if SOS request exists and is active
    const sosRequest = await db('sos_requests')
      .where({ id: sosId, status: 'active' })
      .first();

    if (!sosRequest) {
      return res.status(404).json({
        success: false,
        message: 'SOS request not found or already handled'
      });
    }

    // Check if activist is verified and online
    const activistProfile = await db('activist_profiles')
      .where({ user_id: activistId })
      .first();

    if (!activistProfile || activistProfile.verification_status !== 'verified') {
      return res.status(403).json({
        success: false,
        message: 'Only verified activists can accept SOS requests'
      });
    }

    // Update SOS request
    const [updatedSOS] = await db('sos_requests')
      .where({ id: sosId })
      .update({
        assigned_activist_id: activistId,
        status: 'accepted',
        accepted_at: new Date()
      })
      .returning('*');

    // Notify the youth that help is coming
    io.to(`user_${sosRequest.user_id}`).emit('sos_accepted', {
      sosRequest: updatedSOS,
      activist: {
        id: activistId,
        // Add activist details here
      }
    });

    // Notify other activists that this SOS has been accepted
    const otherActivists = await findNearestActivists(
      sosRequest.latitude, 
      sosRequest.longitude, 
      10
    );
    
    for (const activist of otherActivists) {
      if (activist.user_id !== activistId) {
        io.to(`user_${activist.user_id}`).emit('sos_taken', { sosId });
      }
    }

    res.json({
      success: true,
      message: 'SOS request accepted successfully',
      data: { sosRequest: updatedSOS }
    });

  } catch (error) {
    logger.error('Accept SOS error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Resolve SOS request
router.post('/:id/resolve', async (req: AuthenticatedRequest, res) => {
  try {
    const sosId = req.params.id;
    const userId = req.user!.id;
    const { resolution_notes } = req.body;

    // Check if user has permission to resolve this SOS
    const sosRequest = await db('sos_requests')
      .where({ id: sosId })
      .andWhere(function() {
        this.where({ user_id: userId })
            .orWhere({ assigned_activist_id: userId });
      })
      .first();

    if (!sosRequest) {
      return res.status(404).json({
        success: false,
        message: 'SOS request not found or access denied'
      });
    }

    if (sosRequest.status === 'resolved') {
      return res.status(400).json({
        success: false,
        message: 'SOS request is already resolved'
      });
    }

    // Calculate response time if activist resolved
    let responseTime = null;
    if (sosRequest.assigned_activist_id === userId && sosRequest.accepted_at) {
      responseTime = Math.floor(
        (new Date().getTime() - new Date(sosRequest.accepted_at).getTime()) / (1000 * 60)
      );
    }

    // Update SOS request
    const [updatedSOS] = await db('sos_requests')
      .where({ id: sosId })
      .update({
        status: 'resolved',
        resolved_at: new Date(),
        response_time: responseTime,
        resolution_notes
      })
      .returning('*');

    // Notify relevant parties
    io.to(`user_${sosRequest.user_id}`).emit('sos_resolved', { sosRequest: updatedSOS });
    if (sosRequest.assigned_activist_id) {
      io.to(`user_${sosRequest.assigned_activist_id}`).emit('sos_resolved', { sosRequest: updatedSOS });
    }

    res.json({
      success: true,
      message: 'SOS request resolved successfully',
      data: { sosRequest: updatedSOS }
    });

  } catch (error) {
    logger.error('Resolve SOS error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

export default router;
