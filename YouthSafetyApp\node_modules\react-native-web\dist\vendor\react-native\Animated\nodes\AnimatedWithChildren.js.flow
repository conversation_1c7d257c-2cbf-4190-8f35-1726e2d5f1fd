/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 */

'use strict';

import type { PlatformConfig } from '../AnimatedPlatformConfig';
import AnimatedNode from './AnimatedNode';
import NativeAnimatedHelper from '../NativeAnimatedHelper';
declare class AnimatedWithChildren extends AnimatedNode {
  _children: Array<AnimatedNode>,
  constructor(): any,
  __makeNative(platformConfig: ?PlatformConfig): any,
  __addChild(child: AnimatedNode): void,
  __removeChild(child: AnimatedNode): void,
  __getChildren(): Array<AnimatedNode>,
  __callListeners(value: number): void,
}
export default AnimatedWithChildren;