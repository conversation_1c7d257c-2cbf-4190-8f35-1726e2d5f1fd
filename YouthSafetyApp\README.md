# Youth Safety App

A comprehensive mobile application designed to provide safety, support, and community for young people aged 13-25. The app features emergency SOS functionality, real-time communication, social features, and a network of verified community activists.

## 🌟 Features

### For Youth (13-25)
- **Emergency SOS System**: One-tap emergency alerts with real-time location sharing
- **Anonymous Confessions Room**: Safe space for sharing thoughts and receiving support
- **Social Feed**: Connect with peers and share experiences
- **Real-time Communication**: Video/audio calls and messaging with activists
- **Privacy Controls**: Comprehensive privacy settings and data protection

### For Community Activists
- **Emergency Response**: Accept and respond to SOS requests in real-time
- **Verification System**: KYC process to ensure trusted community members
- **Resource Sharing**: Share knowledge and organize community events
- **Performance Analytics**: Track response times and community impact

### For Administrators
- **User Management**: Comprehensive admin panel for user oversight
- **Content Moderation**: Tools for maintaining safe community spaces
- **Analytics Dashboard**: Platform insights and usage statistics
- **KYC Management**: Activist verification and approval system

## 🏗️ Architecture

### Frontend (React Native + Expo)
- **Framework**: React Native with Expo managed workflow
- **State Management**: Redux Toolkit with RTK Query
- **Navigation**: React Navigation v6
- **UI Components**: Custom components with Expo Vector Icons
- **Maps**: React Native Maps with Google Maps integration
- **Real-time**: Socket.io client for live updates
- **Security**: End-to-end encryption for sensitive data

### Backend (Node.js + TypeScript)
- **Framework**: Express.js with TypeScript
- **Database**: PostgreSQL with Knex.js query builder
- **Authentication**: JWT with refresh tokens
- **Real-time**: Socket.io for WebRTC signaling and live updates
- **File Storage**: AWS S3 for media files
- **Notifications**: Push notifications via Expo
- **SMS**: Twilio for emergency notifications
- **Email**: SendGrid for transactional emails

### Infrastructure
- **Containerization**: Docker with multi-stage builds
- **Orchestration**: Docker Compose for local development
- **Monitoring**: Prometheus + Grafana
- **Logging**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **Reverse Proxy**: Nginx with SSL termination
- **CI/CD**: GitHub Actions with automated testing and deployment

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Docker and Docker Compose
- Expo CLI (`npm install -g @expo/cli`)
- PostgreSQL (if running locally)
- Redis (if running locally)

### Environment Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/youth-safety-app.git
   cd youth-safety-app
   ```

2. **Install dependencies**
   ```bash
   # Frontend
   npm install
   
   # Backend
   cd backend
   npm install
   cd ..
   ```

3. **Environment Configuration**
   ```bash
   # Copy environment files
   cp .env.example .env
   cp backend/.env.example backend/.env
   
   # Edit environment variables
   nano .env
   nano backend/.env
   ```

4. **Database Setup**
   ```bash
   # Start PostgreSQL and Redis with Docker
   docker-compose up postgres redis -d
   
   # Run database migrations
   cd backend
   npm run migrate:latest
   npm run seed:run
   cd ..
   ```

### Development

1. **Start the backend**
   ```bash
   cd backend
   npm run dev
   ```

2. **Start the mobile app**
   ```bash
   # In a new terminal
   npm start
   
   # Or for specific platforms
   npm run android
   npm run ios
   npm run web
   ```

3. **Access the application**
   - Mobile: Scan QR code with Expo Go app
   - Web: http://localhost:19006
   - Backend API: http://localhost:5000
   - API Documentation: http://localhost:5000/docs

## 🧪 Testing

### Frontend Tests
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Backend Tests
```bash
cd backend

# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run integration tests
npm run test:integration
```

### End-to-End Tests
```bash
# Run E2E tests (requires app to be running)
npm run test:e2e
```

## 📦 Deployment

### Using Docker Compose (Recommended)

1. **Production deployment**
   ```bash
   # Build and start all services
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
   
   # View logs
   docker-compose logs -f
   ```

2. **With monitoring stack**
   ```bash
   # Include monitoring services
   docker-compose --profile monitoring up -d
   
   # Access Grafana at http://localhost:3000
   # Access Prometheus at http://localhost:9090
   ```

### Manual Deployment

1. **Backend deployment**
   ```bash
   cd backend
   npm ci --only=production
   npm run build
   npm run migrate:latest
   
   # Start with PM2
   pm2 start ecosystem.config.js --env production
   ```

2. **Mobile app deployment**
   ```bash
   # Build for production
   expo build:android --type apk
   expo build:ios --type archive
   
   # Or publish to Expo
   expo publish --release-channel production
   ```

## 🔧 Configuration

### Environment Variables

#### Frontend (.env)
```env
EXPO_PUBLIC_API_URL=http://localhost:5000
EXPO_PUBLIC_SOCKET_URL=http://localhost:5000
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_key
```

#### Backend (backend/.env)
```env
NODE_ENV=development
PORT=5000
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=youth_safety
REDIS_URL=redis://localhost:6379
JWT_SECRET=your_jwt_secret
ENCRYPTION_KEY=your_encryption_key
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
SENDGRID_API_KEY=your_sendgrid_key
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
AWS_S3_BUCKET=your_s3_bucket
GOOGLE_MAPS_API_KEY=your_google_maps_key
```

## 🔒 Security Features

- **End-to-end encryption** for sensitive communications
- **Data anonymization** for privacy protection
- **Secure authentication** with JWT and refresh tokens
- **Input validation** and sanitization
- **Rate limiting** to prevent abuse
- **GDPR compliance** with data export and deletion
- **Security headers** and HTTPS enforcement
- **Vulnerability scanning** in CI/CD pipeline

## 📱 Platform Support

- **iOS**: 13.0+
- **Android**: API level 21+ (Android 5.0+)
- **Web**: Modern browsers (Chrome, Firefox, Safari, Edge)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow TypeScript best practices
- Write tests for new features
- Update documentation as needed
- Follow the existing code style
- Ensure all CI checks pass

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs.youthsafety.app](https://docs.youthsafety.app)
- **Issues**: [GitHub Issues](https://github.com/your-org/youth-safety-app/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/youth-safety-app/discussions)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- Community activists and youth who provided feedback
- Open source libraries and frameworks used
- Security researchers who helped improve the platform
- Mental health professionals who guided the design

---

**⚠️ Emergency Notice**: This app is designed to supplement, not replace, traditional emergency services. In life-threatening situations, always call your local emergency number (911, 112, etc.) first.
