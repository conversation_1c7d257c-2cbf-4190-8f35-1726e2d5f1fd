"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = hyphenateProperty;

var _hyphenateStyleName = require("hyphenate-style-name");

var _hyphenateStyleName2 = _interopRequireDefault(_hyphenateStyleName);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

function hyphenateProperty(property) {
  return (0, _hyphenateStyleName2["default"])(property);
}