import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('activist_profiles', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.json('credentials').defaultTo('[]');
    table.json('specializations').defaultTo('[]');
    table.json('service_area').defaultTo('{}');
    table.json('availability_schedule').defaultTo('{}');
    table.integer('response_time').defaultTo(0); // in minutes
    table.decimal('rating', 3, 2).defaultTo(0);
    table.integer('total_ratings').defaultTo(0);
    table.enum('verification_status', ['pending', 'verified', 'suspended']).defaultTo('pending');
    table.boolean('is_online').defaultTo(false);
    table.timestamp('last_online_at');
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['verification_status']);
    table.index(['is_online']);
    table.index(['rating']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('activist_profiles');
}
