const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Add support for TypeScript files
config.resolver.sourceExts.push('ts', 'tsx');

// Add support for additional asset types
config.resolver.assetExts.push(
  // Audio formats
  'mp3',
  'wav',
  'aac',
  'm4a',
  // Video formats
  'mp4',
  'mov',
  'avi',
  'mkv',
  // Document formats
  'pdf',
  'doc',
  'docx',
  // Other formats
  'db',
  'zip'
);

// Configure module resolution
config.resolver.alias = {
  '@': path.resolve(__dirname, 'src'),
  '@components': path.resolve(__dirname, 'src/components'),
  '@screens': path.resolve(__dirname, 'src/screens'),
  '@services': path.resolve(__dirname, 'src/services'),
  '@store': path.resolve(__dirname, 'src/store'),
  '@types': path.resolve(__dirname, 'src/types'),
  '@utils': path.resolve(__dirname, 'src/utils'),
  '@navigation': path.resolve(__dirname, 'src/navigation'),
};

// Configure transformer options
config.transformer = {
  ...config.transformer,
  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: true,
    },
  }),
};

// Enable symlinks support
config.resolver.unstable_enableSymlinks = true;

// Configure watchman ignore patterns
config.watchFolders = [
  path.resolve(__dirname, 'src'),
  path.resolve(__dirname, 'node_modules'),
];

// Ignore specific directories and files
config.resolver.blacklistRE = /(.*\/__tests__\/.*|.*\/node_modules\/.*\/node_modules\/.*)/;

module.exports = config;
