import * as Location from 'expo-location';
import { Alert } from 'react-native';
import { LocationData } from '@types/index';

export class LocationService {
  private static instance: LocationService;
  private watchSubscription: Location.LocationSubscription | null = null;
  private currentLocation: LocationData | null = null;

  static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  async requestPermissions(): Promise<boolean> {
    try {
      // Request foreground permissions
      const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
      
      if (foregroundStatus !== 'granted') {
        Alert.alert(
          'Location Permission Required',
          'This app needs location access for emergency SOS functionality. Please enable location permissions in your device settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => Location.requestForegroundPermissionsAsync() }
          ]
        );
        return false;
      }

      // Request background permissions for emergency tracking
      const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();
      
      if (backgroundStatus !== 'granted') {
        Alert.alert(
          'Background Location Required',
          'For emergency situations, this app needs to track your location even when the app is in the background. This helps emergency responders find you quickly.',
          [
            { text: 'Skip', style: 'cancel' },
            { text: 'Enable', onPress: () => Location.requestBackgroundPermissionsAsync() }
          ]
        );
      }

      return true;
    } catch (error) {
      console.error('Error requesting location permissions:', error);
      return false;
    }
  }

  async getCurrentLocation(): Promise<LocationData | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        throw new Error('Location permission not granted');
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.BestForNavigation,
        maximumAge: 10000, // 10 seconds
        timeout: 15000, // 15 seconds
      });

      const address = await this.reverseGeocode(
        location.coords.latitude,
        location.coords.longitude
      );

      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy || 0,
        address: address || 'Unknown location',
        timestamp: new Date(location.timestamp),
      };

      this.currentLocation = locationData;
      return locationData;
    } catch (error) {
      console.error('Error getting current location:', error);
      throw error;
    }
  }

  async startLocationTracking(
    callback: (location: LocationData) => void,
    options: {
      accuracy?: Location.Accuracy;
      timeInterval?: number;
      distanceInterval?: number;
    } = {}
  ): Promise<void> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        throw new Error('Location permission not granted');
      }

      // Stop existing tracking
      await this.stopLocationTracking();

      this.watchSubscription = await Location.watchPositionAsync(
        {
          accuracy: options.accuracy || Location.Accuracy.BestForNavigation,
          timeInterval: options.timeInterval || 5000, // 5 seconds
          distanceInterval: options.distanceInterval || 10, // 10 meters
        },
        async (location) => {
          try {
            const address = await this.reverseGeocode(
              location.coords.latitude,
              location.coords.longitude
            );

            const locationData: LocationData = {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
              accuracy: location.coords.accuracy || 0,
              address: address || 'Unknown location',
              timestamp: new Date(location.timestamp),
            };

            this.currentLocation = locationData;
            callback(locationData);
          } catch (error) {
            console.error('Error processing location update:', error);
          }
        }
      );
    } catch (error) {
      console.error('Error starting location tracking:', error);
      throw error;
    }
  }

  async stopLocationTracking(): Promise<void> {
    if (this.watchSubscription) {
      this.watchSubscription.remove();
      this.watchSubscription = null;
    }
  }

  private async reverseGeocode(latitude: number, longitude: number): Promise<string | null> {
    try {
      const results = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      if (results.length > 0) {
        const result = results[0];
        const addressParts = [
          result.streetNumber,
          result.street,
          result.city,
          result.region,
          result.postalCode,
        ].filter(Boolean);

        return addressParts.join(', ') || 'Unknown location';
      }

      return null;
    } catch (error) {
      console.error('Error reverse geocoding:', error);
      return null;
    }
  }

  getCurrentLocationSync(): LocationData | null {
    return this.currentLocation;
  }

  calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) *
        Math.cos(this.deg2rad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in kilometers
    return distance;
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI / 180);
  }

  async isLocationEnabled(): Promise<boolean> {
    try {
      return await Location.hasServicesEnabledAsync();
    } catch (error) {
      console.error('Error checking location services:', error);
      return false;
    }
  }

  async getLocationAccuracy(): Promise<Location.Accuracy> {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.BestForNavigation,
      });
      
      const accuracy = location.coords.accuracy || 0;
      
      if (accuracy <= 5) return Location.Accuracy.BestForNavigation;
      if (accuracy <= 10) return Location.Accuracy.Highest;
      if (accuracy <= 100) return Location.Accuracy.High;
      if (accuracy <= 1000) return Location.Accuracy.Balanced;
      return Location.Accuracy.Low;
    } catch (error) {
      console.error('Error getting location accuracy:', error);
      return Location.Accuracy.Balanced;
    }
  }

  // Emergency location sharing
  async shareLocationWithEmergencyContacts(
    emergencyContacts: Array<{ name: string; phone: string }>,
    sosId: string
  ): Promise<void> {
    try {
      const location = await this.getCurrentLocation();
      if (!location) {
        throw new Error('Unable to get current location');
      }

      const locationMessage = `EMERGENCY: ${location.address}. Location: https://maps.google.com/?q=${location.latitude},${location.longitude}. SOS ID: ${sosId}`;

      // This would integrate with your SMS service
      // For now, we'll just log the message
      console.log('Emergency location message:', locationMessage);
      
      // In a real implementation, you would send SMS to emergency contacts
      // await SMSService.sendBulkSMS(emergencyContacts.map(c => c.phone), locationMessage);
    } catch (error) {
      console.error('Error sharing location with emergency contacts:', error);
      throw error;
    }
  }

  // Get location history for SOS tracking
  getLocationHistory(): LocationData[] {
    // In a real implementation, you would store location history
    // For now, return current location if available
    return this.currentLocation ? [this.currentLocation] : [];
  }
}

export const locationService = LocationService.getInstance();
