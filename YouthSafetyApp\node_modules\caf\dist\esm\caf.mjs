/*! CAF: caf.mjs
	v15.0.1 (c) 2022 Kyle Simpson
	MIT License: http://getify.mit-license.org
*/
import{CLEANUP_FN,TIMEOUT_TOKEN,UNSET,getSignalReason,cancelToken,signalPromise,processTokenOrSignal,isFunction,invokeAbort}from"./shared.mjs";export default Object.assign(CAF,{cancelToken:cancelToken,delay:delay,timeout:timeout,signalRace:signalRace,signalAll:signalAll,tokenCycle:tokenCycle});export{cancelToken};export{delay};export{timeout};export{signalRace};export{signalAll};export{tokenCycle};function CAF(n){return function instance(e,...r){var l,i;if(({tokenOrSignal:e,signal:l,signalPr:i}=processTokenOrSignal(e)),l.aborted)return i;var o=i.catch((function onCancellation(n){var e=getSignalReason(l);e=e!==UNSET?e:n;try{var r=a.return();throw void 0!==r.value?r.value:e!==UNSET?e:void 0}finally{a=u=o=s=null}})),{it:a,result:u}=runner.call(this,n,l,...r),s=Promise.race([u,o]);if(e!==l&&e[TIMEOUT_TOKEN]){let n=function cancelTimer(r){invokeAbort(e,r),isFunction(e.discard)&&e.discard(),e=n=null};s.then(n,n)}else s.catch((()=>{})),e=null;return r=null,s}}function delay(n,e){var r,l;return"number"==typeof n&&"number"!=typeof e&&([e,n]=[n,e]),n&&({tokenOrSignal:n,signal:r,signalPr:l}=processTokenOrSignal(n)),r&&r.aborted?l:new Promise((function c(n,i){r&&(l.catch((function onAbort(){if(i&&r&&o){let l=getSignalReason(r);clearTimeout(o),i(l!==UNSET?l:`delay (${e}) interrupted`),n=i=o=r=null}})),l=null);var o=setTimeout((function onTimeout(){n(`delayed: ${e}`),n=i=o=r=null}),e)}))}function timeout(n,e="Timeout"){n=Number(n)||0;var r=new cancelToken;return delay(r.signal,n).then((()=>cleanup(e)),cleanup),Object.defineProperty(r,TIMEOUT_TOKEN,{value:!0,writable:!1,enumerable:!1,configurable:!1}),r;function cleanup(...n){invokeAbort(r,n.length>0?n[0]:UNSET),r.discard(),r=null}}function splitSignalPRs(n){return n.reduce((function split(n,e){var r=signalPromise(e);return n[0].push(r),e.pr||n[1].push(r),n}),[[],[]])}function triggerAndCleanup(n,e,r){n.then((function t(n){invokeAbort(e,n),e.discard(),e=null})).then((function t(){for(let n of r)n[CLEANUP_FN]&&n[CLEANUP_FN]();r=null}))}function prCatch(n){return n.catch((n=>n))}function signalRace(n){var e=new cancelToken,[r,l]=splitSignalPRs(n);return triggerAndCleanup(prCatch(Promise.race(r)),e,l),e.signal}function signalAll(n){var e=new cancelToken,[r,l]=splitSignalPRs(n);return triggerAndCleanup(Promise.all(r.map(prCatch)),e,l),e.signal}function tokenCycle(){var n;return function getNextToken(...e){return n&&(invokeAbort(n,e.length>0?e[0]:UNSET),n.discard()),n=new cancelToken}}function runner(n,...e){var r=n.apply(this,e);return n=e=null,{it:r,result:function getNextResult(n){try{var e=r.next(n);n=null}catch(n){return Promise.reject(n)}return function processResult(n){var e=Promise.resolve(n.value);return n.done?r=null:(e=e.then(getNextResult,(function onRejection(n){return Promise.resolve(r.throw(n)).then(processResult)}))).catch((function cleanup(){r=null})),n=null,e}(e)}()}}