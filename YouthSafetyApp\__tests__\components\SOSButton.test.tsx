import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { Alert } from 'react-native';
import SOSButton from '@/components/SOSButton';
import authReducer from '@/store/slices/authSlice';
import sosReducer from '@/store/slices/sosSlice';
import { locationService } from '@/services/locationService';

// Mock dependencies
jest.mock('@/services/locationService');
jest.mock('@/services/api');
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  return {
    ...RN,
    Alert: {
      alert: jest.fn(),
    },
  };
});

const mockStore = configureStore({
  reducer: {
    auth: authReducer,
    sos: sosReducer,
  },
  preloadedState: {
    auth: {
      user: {
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'youth',
        isVerified: true,
      },
      token: 'mock-token',
      isAuthenticated: true,
      isLoading: false,
      error: null,
    },
    sos: {
      activeSOS: null,
      sosHistory: [],
      isCreating: false,
      isTracking: false,
      currentLocation: null,
      nearbyActivists: [],
      error: null,
      isLoading: false,
    },
  },
});

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      {component}
    </Provider>
  );
};

describe('SOSButton', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const { getByText } = renderWithProvider(<SOSButton />);
    
    expect(getByText('Emergency SOS')).toBeTruthy();
  });

  it('shows alert when pressed', async () => {
    const { getByTestId } = renderWithProvider(<SOSButton />);
    const sosButton = getByTestId('sos-button');

    fireEvent.press(sosButton);

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'Emergency SOS',
        'Are you in an emergency situation?',
        expect.arrayContaining([
          expect.objectContaining({ text: 'Cancel' }),
          expect.objectContaining({ text: 'Yes, Send SOS' }),
        ])
      );
    });
  });

  it('requests location permission when SOS is confirmed', async () => {
    const mockGetCurrentLocation = jest.fn().mockResolvedValue({
      latitude: 37.7749,
      longitude: -122.4194,
      accuracy: 10,
      address: '123 Main St, San Francisco, CA',
      timestamp: new Date(),
    });

    (locationService.getCurrentLocation as jest.Mock) = mockGetCurrentLocation;

    const { getByTestId } = renderWithProvider(<SOSButton />);
    const sosButton = getByTestId('sos-button');

    fireEvent.press(sosButton);

    // Simulate user confirming SOS
    const alertCall = (Alert.alert as jest.Mock).mock.calls[0];
    const confirmButton = alertCall[2].find((button: any) => button.text === 'Yes, Send SOS');
    
    await confirmButton.onPress();

    await waitFor(() => {
      expect(mockGetCurrentLocation).toHaveBeenCalled();
    });
  });

  it('handles location permission denial gracefully', async () => {
    const mockGetCurrentLocation = jest.fn().mockRejectedValue(new Error('Location permission denied'));

    (locationService.getCurrentLocation as jest.Mock) = mockGetCurrentLocation;

    const { getByTestId } = renderWithProvider(<SOSButton />);
    const sosButton = getByTestId('sos-button');

    fireEvent.press(sosButton);

    // Simulate user confirming SOS
    const alertCall = (Alert.alert as jest.Mock).mock.calls[0];
    const confirmButton = alertCall[2].find((button: any) => button.text === 'Yes, Send SOS');
    
    await confirmButton.onPress();

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'Location Required',
        expect.stringContaining('location access'),
        expect.any(Array)
      );
    });
  });

  it('shows emergency type selection after location is obtained', async () => {
    const mockGetCurrentLocation = jest.fn().mockResolvedValue({
      latitude: 37.7749,
      longitude: -122.4194,
      accuracy: 10,
      address: '123 Main St, San Francisco, CA',
      timestamp: new Date(),
    });

    (locationService.getCurrentLocation as jest.Mock) = mockGetCurrentLocation;

    const { getByTestId } = renderWithProvider(<SOSButton />);
    const sosButton = getByTestId('sos-button');

    fireEvent.press(sosButton);

    // Simulate user confirming SOS
    const firstAlertCall = (Alert.alert as jest.Mock).mock.calls[0];
    const confirmButton = firstAlertCall[2].find((button: any) => button.text === 'Yes, Send SOS');
    
    await confirmButton.onPress();

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'Emergency Type',
        'What type of emergency is this?',
        expect.arrayContaining([
          expect.objectContaining({ text: 'Medical Emergency' }),
          expect.objectContaining({ text: 'Safety Threat' }),
          expect.objectContaining({ text: 'Mental Health Crisis' }),
          expect.objectContaining({ text: 'General Emergency' }),
        ])
      );
    });
  });

  it('disables button when SOS is being created', () => {
    const storeWithCreating = configureStore({
      reducer: {
        auth: authReducer,
        sos: sosReducer,
      },
      preloadedState: {
        ...mockStore.getState(),
        sos: {
          ...mockStore.getState().sos,
          isCreating: true,
        },
      },
    });

    const { getByTestId } = render(
      <Provider store={storeWithCreating}>
        <SOSButton />
      </Provider>
    );

    const sosButton = getByTestId('sos-button');
    expect(sosButton.props.accessibilityState.disabled).toBe(true);
  });

  it('shows different text when SOS is active', () => {
    const storeWithActiveSOS = configureStore({
      reducer: {
        auth: authReducer,
        sos: sosReducer,
      },
      preloadedState: {
        ...mockStore.getState(),
        sos: {
          ...mockStore.getState().sos,
          activeSOS: {
            id: 'sos-123',
            userId: 'user-123',
            latitude: 37.7749,
            longitude: -122.4194,
            emergencyType: 'medical',
            status: 'active',
            timestamp: new Date(),
          },
        },
      },
    });

    const { getByText } = render(
      <Provider store={storeWithActiveSOS}>
        <SOSButton />
      </Provider>
    );

    expect(getByText('SOS Active')).toBeTruthy();
  });

  it('navigates to SOS tracking when active SOS is pressed', () => {
    const mockNavigate = jest.fn();
    
    // Mock navigation
    jest.doMock('@react-navigation/native', () => ({
      useNavigation: () => ({
        navigate: mockNavigate,
      }),
    }));

    const storeWithActiveSOS = configureStore({
      reducer: {
        auth: authReducer,
        sos: sosReducer,
      },
      preloadedState: {
        ...mockStore.getState(),
        sos: {
          ...mockStore.getState().sos,
          activeSOS: {
            id: 'sos-123',
            userId: 'user-123',
            latitude: 37.7749,
            longitude: -122.4194,
            emergencyType: 'medical',
            status: 'active',
            timestamp: new Date(),
          },
        },
      },
    });

    const { getByTestId } = render(
      <Provider store={storeWithActiveSOS}>
        <SOSButton />
      </Provider>
    );

    const sosButton = getByTestId('sos-button');
    fireEvent.press(sosButton);

    expect(mockNavigate).toHaveBeenCalledWith('SOSTracking', {
      sosRequest: expect.any(Object),
    });
  });

  it('handles network errors gracefully', async () => {
    const mockGetCurrentLocation = jest.fn().mockResolvedValue({
      latitude: 37.7749,
      longitude: -122.4194,
      accuracy: 10,
      address: '123 Main St, San Francisco, CA',
      timestamp: new Date(),
    });

    (locationService.getCurrentLocation as jest.Mock) = mockGetCurrentLocation;

    // Mock API to reject
    const mockCreateSOS = jest.fn().mockRejectedValue(new Error('Network error'));
    jest.doMock('@/services/api', () => ({
      sosAPI: {
        create: mockCreateSOS,
      },
    }));

    const { getByTestId } = renderWithProvider(<SOSButton />);
    const sosButton = getByTestId('sos-button');

    fireEvent.press(sosButton);

    // Simulate user confirming SOS and selecting emergency type
    const firstAlertCall = (Alert.alert as jest.Mock).mock.calls[0];
    const confirmButton = firstAlertCall[2].find((button: any) => button.text === 'Yes, Send SOS');
    
    await confirmButton.onPress();

    await waitFor(() => {
      const secondAlertCall = (Alert.alert as jest.Mock).mock.calls[1];
      const medicalButton = secondAlertCall[2].find((button: any) => button.text === 'Medical Emergency');
      medicalButton.onPress();
    });

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'Error',
        expect.stringContaining('Failed to send SOS'),
        expect.any(Array)
      );
    });
  });
});
