import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('sos_requests', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.uuid('assigned_activist_id').references('id').inTable('users').nullable();
    table.timestamp('timestamp').defaultTo(knex.fn.now());
    table.decimal('latitude', 10, 8).notNullable();
    table.decimal('longitude', 11, 8).notNullable();
    table.decimal('accuracy', 8, 2);
    table.text('address');
    table.enum('emergency_type', ['medical', 'safety', 'mental_health', 'general']).notNullable();
    table.json('medical_info').defaultTo('{}');
    table.enum('status', ['active', 'accepted', 'resolved', 'cancelled']).defaultTo('active');
    table.enum('priority', ['low', 'medium', 'high', 'critical']).defaultTo('medium');
    table.text('description');
    table.string('audio_recording_url');
    table.integer('response_time'); // in minutes
    table.timestamp('accepted_at');
    table.timestamp('resolved_at');
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['assigned_activist_id']);
    table.index(['status']);
    table.index(['emergency_type']);
    table.index(['priority']);
    table.index(['timestamp']);
    table.index(['latitude', 'longitude']); // For geospatial queries
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('sos_requests');
}
