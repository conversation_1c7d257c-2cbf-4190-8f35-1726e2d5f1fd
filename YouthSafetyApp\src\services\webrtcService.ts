import { RTC<PERSON>eerConnection, RTCIceCandidate, RTCSessionDescription, mediaDevices } from 'react-native-webrtc';
import { socketService } from './socketService';

interface CallParticipant {
  id: string;
  name: string;
  avatar?: string;
}

interface CallSession {
  id: string;
  participants: CallParticipant[];
  type: 'audio' | 'video';
  status: 'initiating' | 'ringing' | 'connected' | 'ended';
  startTime?: Date;
  endTime?: Date;
}

class WebRTCService {
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  private currentCall: CallSession | null = null;
  private isInitiator = false;

  // ICE servers configuration
  private iceServers = [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' },
    // Add TURN servers for production
    // {
    //   urls: 'turn:your-turn-server.com:3478',
    //   username: 'username',
    //   credential: 'password'
    // }
  ];

  constructor() {
    this.setupSocketListeners();
  }

  private setupSocketListeners(): void {
    // Listen for WebRTC signaling events from socket
    // These would be implemented in the socket service
  }

  async initializeCall(
    recipientId: string,
    callType: 'audio' | 'video' = 'video'
  ): Promise<CallSession> {
    try {
      this.isInitiator = true;
      
      // Create call session
      this.currentCall = {
        id: Date.now().toString(),
        participants: [
          { id: 'self', name: 'You' },
          { id: recipientId, name: 'Recipient' }
        ],
        type: callType,
        status: 'initiating',
      };

      // Get user media
      await this.getUserMedia(callType === 'video');

      // Create peer connection
      await this.createPeerConnection();

      // Add local stream to peer connection
      if (this.localStream) {
        this.localStream.getTracks().forEach(track => {
          this.peerConnection?.addTrack(track, this.localStream!);
        });
      }

      // Create and send offer
      const offer = await this.peerConnection!.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: callType === 'video',
      });

      await this.peerConnection!.setLocalDescription(offer);
      
      // Send offer through socket
      socketService.sendWebRTCOffer(recipientId, offer);
      socketService.initiateCall(recipientId, callType);

      this.currentCall.status = 'ringing';
      return this.currentCall;
    } catch (error) {
      console.error('Error initializing call:', error);
      throw error;
    }
  }

  async acceptCall(callId: string, callType: 'audio' | 'video' = 'video'): Promise<void> {
    try {
      this.isInitiator = false;
      
      // Get user media
      await this.getUserMedia(callType === 'video');

      // Create peer connection
      await this.createPeerConnection();

      // Add local stream to peer connection
      if (this.localStream) {
        this.localStream.getTracks().forEach(track => {
          this.peerConnection?.addTrack(track, this.localStream!);
        });
      }

      // Accept call through socket
      socketService.acceptCall(callId);
    } catch (error) {
      console.error('Error accepting call:', error);
      throw error;
    }
  }

  async handleOffer(offer: RTCSessionDescriptionInit, senderId: string): Promise<void> {
    try {
      if (!this.peerConnection) {
        await this.createPeerConnection();
      }

      await this.peerConnection!.setRemoteDescription(new RTCSessionDescription(offer));

      // Create answer
      const answer = await this.peerConnection!.createAnswer();
      await this.peerConnection!.setLocalDescription(answer);

      // Send answer through socket
      socketService.sendWebRTCAnswer(senderId, answer);
    } catch (error) {
      console.error('Error handling offer:', error);
      throw error;
    }
  }

  async handleAnswer(answer: RTCSessionDescriptionInit): Promise<void> {
    try {
      await this.peerConnection!.setRemoteDescription(new RTCSessionDescription(answer));
      
      if (this.currentCall) {
        this.currentCall.status = 'connected';
        this.currentCall.startTime = new Date();
      }
    } catch (error) {
      console.error('Error handling answer:', error);
      throw error;
    }
  }

  async handleICECandidate(candidate: RTCIceCandidateInit): Promise<void> {
    try {
      if (this.peerConnection) {
        await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
      }
    } catch (error) {
      console.error('Error handling ICE candidate:', error);
    }
  }

  private async createPeerConnection(): Promise<void> {
    this.peerConnection = new RTCPeerConnection({
      iceServers: this.iceServers,
    });

    // Handle ICE candidates
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate && this.currentCall) {
        const recipientId = this.currentCall.participants.find(p => p.id !== 'self')?.id;
        if (recipientId) {
          socketService.sendICECandidate(recipientId, event.candidate);
        }
      }
    };

    // Handle remote stream
    this.peerConnection.ontrack = (event) => {
      if (event.streams && event.streams[0]) {
        this.remoteStream = event.streams[0];
        // Notify UI about remote stream
        this.onRemoteStreamReceived?.(this.remoteStream);
      }
    };

    // Handle connection state changes
    this.peerConnection.onconnectionstatechange = () => {
      const state = this.peerConnection?.connectionState;
      console.log('Connection state changed:', state);
      
      if (state === 'connected' && this.currentCall) {
        this.currentCall.status = 'connected';
        this.currentCall.startTime = new Date();
      } else if (state === 'disconnected' || state === 'failed') {
        this.endCall();
      }
    };
  }

  private async getUserMedia(video: boolean = true): Promise<void> {
    try {
      const constraints = {
        audio: true,
        video: video ? {
          width: { min: 640, ideal: 1280 },
          height: { min: 480, ideal: 720 },
          frameRate: { min: 16, ideal: 30 },
          facingMode: 'user',
        } : false,
      };

      this.localStream = await mediaDevices.getUserMedia(constraints);
      
      // Notify UI about local stream
      this.onLocalStreamReceived?.(this.localStream);
    } catch (error) {
      console.error('Error getting user media:', error);
      throw error;
    }
  }

  endCall(): void {
    try {
      // Close peer connection
      if (this.peerConnection) {
        this.peerConnection.close();
        this.peerConnection = null;
      }

      // Stop local stream
      if (this.localStream) {
        this.localStream.getTracks().forEach(track => track.stop());
        this.localStream = null;
      }

      // Clear remote stream
      this.remoteStream = null;

      // Update call status
      if (this.currentCall) {
        this.currentCall.status = 'ended';
        this.currentCall.endTime = new Date();
        
        // Notify socket
        socketService.endCall(this.currentCall.id);
      }

      // Notify UI
      this.onCallEnded?.(this.currentCall);
      
      this.currentCall = null;
      this.isInitiator = false;
    } catch (error) {
      console.error('Error ending call:', error);
    }
  }

  rejectCall(callId: string): void {
    socketService.rejectCall(callId);
    this.endCall();
  }

  // Toggle audio/video during call
  toggleAudio(): boolean {
    if (this.localStream) {
      const audioTrack = this.localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        return audioTrack.enabled;
      }
    }
    return false;
  }

  toggleVideo(): boolean {
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        return videoTrack.enabled;
      }
    }
    return false;
  }

  // Switch camera (front/back)
  async switchCamera(): Promise<void> {
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        // @ts-ignore - react-native-webrtc specific method
        videoTrack._switchCamera();
      }
    }
  }

  // Get current call information
  getCurrentCall(): CallSession | null {
    return this.currentCall;
  }

  getLocalStream(): MediaStream | null {
    return this.localStream;
  }

  getRemoteStream(): MediaStream | null {
    return this.remoteStream;
  }

  // Event handlers (to be set by UI components)
  onLocalStreamReceived?: (stream: MediaStream) => void;
  onRemoteStreamReceived?: (stream: MediaStream) => void;
  onCallEnded?: (call: CallSession | null) => void;
  onIncomingCall?: (call: CallSession) => void;
}

export const webrtcService = new WebRTCService();
