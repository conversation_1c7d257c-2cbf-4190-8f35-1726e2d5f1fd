import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Location from 'expo-location';
import * as Notifications from 'expo-notifications';
import * as LocalAuthentication from 'expo-local-authentication';
import NetInfo from '@react-native-community/netinfo';
import { socketService } from './socketService';
import { notificationService } from './notificationService';
import { locationService } from './locationService';
import { encryptionService } from './encryptionService';

// Configure notification handler
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

export interface InitializationResult {
  permissions: {
    location: boolean;
    notifications: boolean;
    biometric: boolean;
  };
  services: {
    socket: boolean;
    encryption: boolean;
    location: boolean;
    notifications: boolean;
  };
  deviceInfo: {
    hasNetworkConnection: boolean;
    biometricSupported: boolean;
    locationServicesEnabled: boolean;
  };
}

export async function initializeServices(): Promise<InitializationResult> {
  console.log('🚀 Initializing Youth Safety App services...');

  const result: InitializationResult = {
    permissions: {
      location: false,
      notifications: false,
      biometric: false,
    },
    services: {
      socket: false,
      encryption: false,
      location: false,
      notifications: false,
    },
    deviceInfo: {
      hasNetworkConnection: false,
      biometricSupported: false,
      locationServicesEnabled: false,
    },
  };

  try {
    // Check network connectivity
    const networkState = await NetInfo.fetch();
    result.deviceInfo.hasNetworkConnection = networkState.isConnected ?? false;
    console.log('📶 Network status:', result.deviceInfo.hasNetworkConnection ? 'Connected' : 'Disconnected');

    // Initialize encryption service
    try {
      await encryptionService.initialize();
      result.services.encryption = true;
      console.log('🔐 Encryption service initialized');
    } catch (error) {
      console.warn('⚠️ Encryption service initialization failed:', error);
    }

    // Request and check permissions
    await initializePermissions(result);

    // Initialize location services
    if (result.permissions.location) {
      try {
        await locationService.initialize();
        result.services.location = true;
        console.log('📍 Location service initialized');
      } catch (error) {
        console.warn('⚠️ Location service initialization failed:', error);
      }
    }

    // Initialize notification services
    if (result.permissions.notifications) {
      try {
        await notificationService.initialize();
        result.services.notifications = true;
        console.log('🔔 Notification service initialized');
      } catch (error) {
        console.warn('⚠️ Notification service initialization failed:', error);
      }
    }

    // Initialize socket service (if network is available)
    if (result.deviceInfo.hasNetworkConnection) {
      try {
        await socketService.initialize();
        result.services.socket = true;
        console.log('🔌 Socket service initialized');
      } catch (error) {
        console.warn('⚠️ Socket service initialization failed:', error);
      }
    }

    // Check device capabilities
    await checkDeviceCapabilities(result);

    // Load user preferences
    await loadUserPreferences();

    // Setup error handlers
    setupGlobalErrorHandlers();

    console.log('✅ App initialization completed successfully');
    console.log('📊 Initialization result:', result);

    return result;
  } catch (error) {
    console.error('❌ App initialization failed:', error);
    throw error;
  }
}

async function initializePermissions(result: InitializationResult): Promise<void> {
  console.log('🔑 Requesting permissions...');

  // Location permissions
  try {
    const { status: locationStatus } = await Location.requestForegroundPermissionsAsync();
    result.permissions.location = locationStatus === 'granted';
    
    if (result.permissions.location) {
      // Also request background location for emergency features
      const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();
      console.log('📍 Location permissions:', locationStatus, 'Background:', backgroundStatus);
    }
  } catch (error) {
    console.warn('⚠️ Location permission request failed:', error);
  }

  // Notification permissions
  try {
    const { status: notificationStatus } = await Notifications.requestPermissionsAsync();
    result.permissions.notifications = notificationStatus === 'granted';
    console.log('🔔 Notification permissions:', notificationStatus);
  } catch (error) {
    console.warn('⚠️ Notification permission request failed:', error);
  }

  // Biometric permissions (check availability)
  try {
    const biometricAvailable = await LocalAuthentication.hasHardwareAsync();
    const biometricEnrolled = await LocalAuthentication.isEnrolledAsync();
    result.permissions.biometric = biometricAvailable && biometricEnrolled;
    console.log('👆 Biometric available:', biometricAvailable, 'Enrolled:', biometricEnrolled);
  } catch (error) {
    console.warn('⚠️ Biometric check failed:', error);
  }
}

async function checkDeviceCapabilities(result: InitializationResult): Promise<void> {
  console.log('📱 Checking device capabilities...');

  // Check if location services are enabled
  try {
    result.deviceInfo.locationServicesEnabled = await Location.hasServicesEnabledAsync();
    console.log('📍 Location services enabled:', result.deviceInfo.locationServicesEnabled);
  } catch (error) {
    console.warn('⚠️ Location services check failed:', error);
  }

  // Check biometric support
  try {
    result.deviceInfo.biometricSupported = await LocalAuthentication.hasHardwareAsync();
    console.log('👆 Biometric supported:', result.deviceInfo.biometricSupported);
  } catch (error) {
    console.warn('⚠️ Biometric support check failed:', error);
  }
}

async function loadUserPreferences(): Promise<void> {
  console.log('⚙️ Loading user preferences...');

  try {
    const preferences = await AsyncStorage.getItem('userPreferences');
    if (preferences) {
      const parsedPreferences = JSON.parse(preferences);
      console.log('✅ User preferences loaded:', Object.keys(parsedPreferences));
    } else {
      console.log('ℹ️ No user preferences found, using defaults');
    }
  } catch (error) {
    console.warn('⚠️ Failed to load user preferences:', error);
  }
}

function setupGlobalErrorHandlers(): void {
  console.log('🛡️ Setting up global error handlers...');

  // Handle unhandled promise rejections
  const originalHandler = ErrorUtils.getGlobalHandler();
  ErrorUtils.setGlobalHandler((error, isFatal) => {
    console.error('🚨 Global error:', error, 'Fatal:', isFatal);
    
    // Log to crash reporting service
    // crashlytics().recordError(error);
    
    // Call original handler
    originalHandler(error, isFatal);
  });

  // Handle network state changes
  NetInfo.addEventListener(state => {
    console.log('📶 Network state changed:', state.isConnected ? 'Connected' : 'Disconnected');
    
    if (state.isConnected && !socketService.isConnected()) {
      console.log('🔌 Reconnecting socket service...');
      socketService.connect().catch(error => {
        console.warn('⚠️ Socket reconnection failed:', error);
      });
    }
  });
}

export async function reinitializeServices(): Promise<void> {
  console.log('🔄 Reinitializing services...');
  
  try {
    // Reinitialize critical services
    await socketService.reconnect();
    await notificationService.refresh();
    console.log('✅ Services reinitialized successfully');
  } catch (error) {
    console.error('❌ Service reinitialization failed:', error);
    throw error;
  }
}

export async function cleanupServices(): Promise<void> {
  console.log('🧹 Cleaning up services...');
  
  try {
    await socketService.disconnect();
    await notificationService.cleanup();
    console.log('✅ Services cleaned up successfully');
  } catch (error) {
    console.error('❌ Service cleanup failed:', error);
  }
}
