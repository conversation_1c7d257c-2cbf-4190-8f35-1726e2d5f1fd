import AsyncStorage from '@react-native-async-storage/async-storage';
import { encryptionService } from './encryptionService';
import { usersAPI } from './api';

export interface PrivacySettings {
  profileVisibility: 'public' | 'friends' | 'private';
  locationSharing: 'always' | 'emergency_only' | 'never';
  dataCollection: boolean;
  analyticsOptOut: boolean;
  marketingOptOut: boolean;
  thirdPartySharing: boolean;
  anonymousPosting: boolean;
  emergencyContactsVisible: boolean;
  medicalInfoVisible: boolean;
}

export interface DataRetentionSettings {
  chatHistory: number; // days
  locationHistory: number; // days
  activityLogs: number; // days
  mediaFiles: number; // days
}

export class PrivacyService {
  private static instance: PrivacyService;
  private privacySettings: PrivacySettings | null = null;
  private dataRetentionSettings: DataRetentionSettings | null = null;

  static getInstance(): PrivacyService {
    if (!PrivacyService.instance) {
      PrivacyService.instance = new PrivacyService();
    }
    return PrivacyService.instance;
  }

  async initialize(): Promise<void> {
    await this.loadPrivacySettings();
    await this.loadDataRetentionSettings();
  }

  // Privacy Settings Management
  async loadPrivacySettings(): Promise<PrivacySettings> {
    try {
      const stored = await AsyncStorage.getItem('privacy_settings');
      if (stored) {
        this.privacySettings = JSON.parse(stored);
      } else {
        // Default privacy settings (privacy-first approach)
        this.privacySettings = {
          profileVisibility: 'friends',
          locationSharing: 'emergency_only',
          dataCollection: false,
          analyticsOptOut: true,
          marketingOptOut: true,
          thirdPartySharing: false,
          anonymousPosting: true,
          emergencyContactsVisible: false,
          medicalInfoVisible: false,
        };
        await this.savePrivacySettings();
      }
      return this.privacySettings;
    } catch (error) {
      console.error('Error loading privacy settings:', error);
      throw new Error('Failed to load privacy settings');
    }
  }

  async updatePrivacySettings(settings: Partial<PrivacySettings>): Promise<void> {
    try {
      if (!this.privacySettings) {
        await this.loadPrivacySettings();
      }

      this.privacySettings = { ...this.privacySettings!, ...settings };
      await this.savePrivacySettings();

      // Sync with backend
      await usersAPI.updatePreferences({ privacy: this.privacySettings });
    } catch (error) {
      console.error('Error updating privacy settings:', error);
      throw new Error('Failed to update privacy settings');
    }
  }

  private async savePrivacySettings(): Promise<void> {
    if (this.privacySettings) {
      await AsyncStorage.setItem('privacy_settings', JSON.stringify(this.privacySettings));
    }
  }

  getPrivacySettings(): PrivacySettings | null {
    return this.privacySettings;
  }

  // Data Retention Management
  async loadDataRetentionSettings(): Promise<DataRetentionSettings> {
    try {
      const stored = await AsyncStorage.getItem('data_retention_settings');
      if (stored) {
        this.dataRetentionSettings = JSON.parse(stored);
      } else {
        // Default retention settings (minimal retention)
        this.dataRetentionSettings = {
          chatHistory: 30,
          locationHistory: 7,
          activityLogs: 30,
          mediaFiles: 30,
        };
        await this.saveDataRetentionSettings();
      }
      return this.dataRetentionSettings;
    } catch (error) {
      console.error('Error loading data retention settings:', error);
      throw new Error('Failed to load data retention settings');
    }
  }

  async updateDataRetentionSettings(settings: Partial<DataRetentionSettings>): Promise<void> {
    try {
      if (!this.dataRetentionSettings) {
        await this.loadDataRetentionSettings();
      }

      this.dataRetentionSettings = { ...this.dataRetentionSettings!, ...settings };
      await this.saveDataRetentionSettings();
    } catch (error) {
      console.error('Error updating data retention settings:', error);
      throw new Error('Failed to update data retention settings');
    }
  }

  private async saveDataRetentionSettings(): Promise<void> {
    if (this.dataRetentionSettings) {
      await AsyncStorage.setItem('data_retention_settings', JSON.stringify(this.dataRetentionSettings));
    }
  }

  // Data Anonymization
  anonymizeUserData(userData: any): any {
    const anonymized = { ...userData };
    
    // Remove or hash personally identifiable information
    delete anonymized.firstName;
    delete anonymized.lastName;
    delete anonymized.email;
    delete anonymized.phone;
    delete anonymized.avatar;
    
    // Hash user ID for analytics while maintaining uniqueness
    if (anonymized.id) {
      anonymized.id = encryptionService.hash(anonymized.id);
    }

    // Remove location data if not consented
    if (!this.privacySettings?.dataCollection) {
      delete anonymized.location;
      delete anonymized.address;
    }

    return anonymized;
  }

  // Location Privacy
  shouldShareLocation(context: 'emergency' | 'social' | 'analytics'): boolean {
    if (!this.privacySettings) return false;

    switch (context) {
      case 'emergency':
        return this.privacySettings.locationSharing !== 'never';
      case 'social':
        return this.privacySettings.locationSharing === 'always';
      case 'analytics':
        return this.privacySettings.locationSharing === 'always' && this.privacySettings.dataCollection;
      default:
        return false;
    }
  }

  // Data Minimization
  minimizeDataForContext(data: any, context: 'emergency' | 'social' | 'analytics' | 'support'): any {
    const minimized = { ...data };

    switch (context) {
      case 'emergency':
        // Keep only essential emergency data
        return {
          id: minimized.id,
          location: minimized.location,
          medicalInfo: minimized.medicalInfo,
          emergencyContacts: minimized.emergencyContacts,
          timestamp: minimized.timestamp,
        };

      case 'social':
        // Remove sensitive personal data
        delete minimized.medicalInfo;
        delete minimized.emergencyContacts;
        delete minimized.phone;
        if (this.privacySettings?.anonymousPosting) {
          delete minimized.firstName;
          delete minimized.lastName;
          delete minimized.avatar;
        }
        return minimized;

      case 'analytics':
        // Anonymize for analytics
        return this.anonymizeUserData(minimized);

      case 'support':
        // Keep data needed for support but remove unnecessary details
        delete minimized.emergencyContacts;
        delete minimized.medicalInfo;
        return minimized;

      default:
        return minimized;
    }
  }

  // Consent Management
  async recordConsent(consentType: string, granted: boolean): Promise<void> {
    try {
      const consentRecord = {
        type: consentType,
        granted,
        timestamp: new Date().toISOString(),
        version: '1.0', // Privacy policy version
      };

      const existingConsents = await this.getConsentHistory();
      existingConsents.push(consentRecord);

      await encryptionService.secureStore('consent_history', JSON.stringify(existingConsents));
    } catch (error) {
      console.error('Error recording consent:', error);
    }
  }

  async getConsentHistory(): Promise<any[]> {
    try {
      const stored = await encryptionService.secureRetrieve('consent_history');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error getting consent history:', error);
      return [];
    }
  }

  async hasValidConsent(consentType: string): Promise<boolean> {
    try {
      const consents = await this.getConsentHistory();
      const latestConsent = consents
        .filter(c => c.type === consentType)
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0];

      return latestConsent?.granted === true;
    } catch (error) {
      console.error('Error checking consent:', error);
      return false;
    }
  }

  // Data Export (GDPR compliance)
  async exportUserData(): Promise<any> {
    try {
      // Collect all user data from various sources
      const userData = {
        profile: await this.getUserProfile(),
        posts: await this.getUserPosts(),
        messages: await this.getUserMessages(),
        sosHistory: await this.getSOSHistory(),
        privacySettings: this.privacySettings,
        consentHistory: await this.getConsentHistory(),
        exportDate: new Date().toISOString(),
      };

      return userData;
    } catch (error) {
      console.error('Error exporting user data:', error);
      throw new Error('Failed to export user data');
    }
  }

  // Data Deletion (Right to be forgotten)
  async deleteAllUserData(): Promise<void> {
    try {
      // Clear local storage
      await AsyncStorage.multiRemove([
        'privacy_settings',
        'data_retention_settings',
        'user_profile',
        'chat_history',
        'location_history',
      ]);

      // Clear secure storage
      await encryptionService.secureRemove('consent_history');
      await encryptionService.secureRemove('medical_info');
      await encryptionService.secureRemove('emergency_contacts');

      // Request backend deletion
      // await usersAPI.deleteAccount();

      // Clear encryption keys
      encryptionService.clearEncryptionKey();
    } catch (error) {
      console.error('Error deleting user data:', error);
      throw new Error('Failed to delete user data');
    }
  }

  // Data Breach Response
  async handleDataBreach(breachDetails: any): Promise<void> {
    try {
      // Log the breach
      console.error('Data breach detected:', encryptionService.sanitizeForLogging(breachDetails));

      // Rotate encryption keys
      await encryptionService.rotateEncryptionKey();

      // Notify user (this would typically be done through push notifications)
      await this.notifyUserOfBreach(breachDetails);

      // Record breach for compliance
      await this.recordSecurityIncident('data_breach', breachDetails);
    } catch (error) {
      console.error('Error handling data breach:', error);
    }
  }

  private async notifyUserOfBreach(breachDetails: any): Promise<void> {
    // Implementation would depend on notification service
    console.log('User notified of data breach');
  }

  private async recordSecurityIncident(type: string, details: any): Promise<void> {
    try {
      const incident = {
        type,
        details: encryptionService.sanitizeForLogging(details),
        timestamp: new Date().toISOString(),
        resolved: false,
      };

      const incidents = await this.getSecurityIncidents();
      incidents.push(incident);

      await encryptionService.secureStore('security_incidents', JSON.stringify(incidents));
    } catch (error) {
      console.error('Error recording security incident:', error);
    }
  }

  private async getSecurityIncidents(): Promise<any[]> {
    try {
      const stored = await encryptionService.secureRetrieve('security_incidents');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error getting security incidents:', error);
      return [];
    }
  }

  // Helper methods for data export
  private async getUserProfile(): Promise<any> {
    // Implementation would fetch user profile data
    return {};
  }

  private async getUserPosts(): Promise<any[]> {
    // Implementation would fetch user posts
    return [];
  }

  private async getUserMessages(): Promise<any[]> {
    // Implementation would fetch user messages
    return [];
  }

  private async getSOSHistory(): Promise<any[]> {
    // Implementation would fetch SOS history
    return [];
  }

  // Cleanup expired data based on retention settings
  async cleanupExpiredData(): Promise<void> {
    if (!this.dataRetentionSettings) return;

    try {
      const now = new Date();
      
      // Clean up chat history
      // Implementation would remove chat messages older than retention period
      
      // Clean up location history
      // Implementation would remove location data older than retention period
      
      // Clean up activity logs
      // Implementation would remove activity logs older than retention period
      
      // Clean up media files
      // Implementation would remove media files older than retention period
      
      console.log('Expired data cleanup completed');
    } catch (error) {
      console.error('Error during data cleanup:', error);
    }
  }
}

export const privacyService = PrivacyService.getInstance();
