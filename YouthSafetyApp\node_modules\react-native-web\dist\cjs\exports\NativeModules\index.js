"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _UIManager = _interopRequireDefault(require("../UIManager"));
/**
 * Copyright (c) <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * 
 */

// NativeModules shim
var NativeModules = {
  UIManager: _UIManager.default
};
var _default = exports.default = NativeModules;
module.exports = exports.default;