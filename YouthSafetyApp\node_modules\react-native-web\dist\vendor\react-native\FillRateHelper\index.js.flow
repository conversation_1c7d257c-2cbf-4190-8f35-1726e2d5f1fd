/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

'use strict';

import type { FrameMetricProps } from '../VirtualizedList/VirtualizedListProps';
export type FillRateInfo = Info;
declare class Info {
  any_blank_count: number,
  any_blank_ms: number,
  any_blank_speed_sum: number,
  mostly_blank_count: number,
  mostly_blank_ms: number,
  pixels_blank: number,
  pixels_sampled: number,
  pixels_scrolled: number,
  total_time_spent: number,
  sample_count: number,
}
type FrameMetrics = {
  inLayout?: boolean,
  length: number,
  offset: number,
  ...
};
const DEBUG = false;
let _listeners: Array<(Info) => void> = [];
let _minSampleCount = 10;
let _sampleRate = DEBUG ? 1 : null;

/**
 * A helper class for detecting when the maximem fill rate of `VirtualizedList` is exceeded.
 * By default the sampling rate is set to zero and this will do nothing. If you want to collect
 * samples (e.g. to log them), make sure to call `FillRateHelper.setSampleRate(0.0-1.0)`.
 *
 * Listeners and sample rate are global for all `VirtualizedList`s - typical usage will combine with
 * `SceneTracker.getActiveScene` to determine the context of the events.
 */
declare class FillRateHelper {
  _anyBlankStartTime: ?number,
  _enabled: any,
  _getFrameMetrics: (index: number, props: FrameMetricProps) => ?FrameMetrics,
  _info: Info,
  _mostlyBlankStartTime: ?number,
  _samplesStartTime: ?number,
  static addListener(callback: (FillRateInfo) => void): {
    remove: () => void,
    ...
  },
  static setSampleRate(sampleRate: number): any,
  static setMinSampleCount(minSampleCount: number): any,
  constructor(getFrameMetrics: (index: number, props: FrameMetricProps) => ?FrameMetrics): any,
  activate(): any,
  deactivateAndFlush(): any,
  computeBlankness(props: {
    ...FrameMetricProps,
    initialNumToRender?: ?number,
    ...
  }, cellsAroundViewport: {
    first: number,
    last: number,
    ...
  }, scrollMetrics: {
    dOffset: number,
    offset: number,
    velocity: number,
    visibleLength: number,
    ...
  }): number,
  enabled(): boolean,
  _resetData(): any,
}
export default FillRateHelper;