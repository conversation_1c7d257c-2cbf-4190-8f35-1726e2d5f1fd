{"name": "youth-safety-backend", "version": "1.0.0", "description": "Backend API for Youth Safety App", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon src/server.ts", "build": "tsc", "test": "jest", "migrate": "knex migrate:latest", "seed": "knex seed:run", "lint": "eslint src/**/*.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.9.2", "pg": "^8.11.3", "knex": "^2.5.1", "socket.io": "^4.7.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.4", "twilio": "^4.14.0", "redis": "^4.6.7", "rate-limiter-flexible": "^2.4.2", "crypto": "^1.0.1", "uuid": "^9.0.0", "moment": "^2.29.4", "geolib": "^3.3.4", "sharp": "^0.32.4", "winston": "^3.10.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/pg": "^8.10.2", "@types/multer": "^1.4.7", "@types/nodemailer": "^6.4.9", "@types/uuid": "^9.0.2", "@types/node": "^20.4.5", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "eslint": "^8.45.0", "jest": "^29.6.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}, "keywords": ["youth-safety", "emergency-sos", "social-platform", "community-activism"]}