import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { sosAPI } from '@/services/api';
import { SOSRequest, LocationData } from '@types/index';

interface SOSState {
  activeSOS: SOSRequest | null;
  sosHistory: SOSRequest[];
  isCreating: boolean;
  isTracking: boolean;
  currentLocation: LocationData | null;
  nearbyActivists: any[];
  error: string | null;
  isLoading: boolean;
}

const initialState: SOSState = {
  activeSOS: null,
  sosHistory: [],
  isCreating: false,
  isTracking: false,
  currentLocation: null,
  nearbyActivists: [],
  error: null,
  isLoading: false,
};

// Async thunks
export const createSOS = createAsyncThunk(
  'sos/create',
  async (sosData: {
    latitude: number;
    longitude: number;
    accuracy?: number;
    address?: string;
    emergencyType: 'medical' | 'safety' | 'mental_health' | 'general';
    description?: string;
    priority?: 'low' | 'medium' | 'high' | 'critical';
  }, { rejectWithValue }) => {
    try {
      const response = await sosAPI.create(sosData);
      return response.data.sosRequest;
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || 'Failed to create SOS request',
      });
    }
  }
);

export const fetchSOSHistory = createAsyncThunk(
  'sos/fetchHistory',
  async ({ page = 1, limit = 10 }: { page?: number; limit?: number } = {}, { rejectWithValue }) => {
    try {
      const response = await sosAPI.getMyRequests(page, limit);
      return response.data;
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || 'Failed to fetch SOS history',
      });
    }
  }
);

export const acceptSOS = createAsyncThunk(
  'sos/accept',
  async (sosId: string, { rejectWithValue }) => {
    try {
      const response = await sosAPI.accept(sosId);
      return response.data.sosRequest;
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || 'Failed to accept SOS request',
      });
    }
  }
);

export const resolveSOS = createAsyncThunk(
  'sos/resolve',
  async ({ sosId, resolutionNotes }: { sosId: string; resolutionNotes?: string }, { rejectWithValue }) => {
    try {
      const response = await sosAPI.resolve(sosId, resolutionNotes);
      return response.data.sosRequest;
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || 'Failed to resolve SOS request',
      });
    }
  }
);

export const cancelSOS = createAsyncThunk(
  'sos/cancel',
  async ({ sosId, reason }: { sosId: string; reason?: string }, { rejectWithValue }) => {
    try {
      const response = await sosAPI.cancel(sosId, reason);
      return response.data.sosRequest;
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || 'Failed to cancel SOS request',
      });
    }
  }
);

export const updateSOSLocation = createAsyncThunk(
  'sos/updateLocation',
  async ({ sosId, location }: { sosId: string; location: { latitude: number; longitude: number } }, { rejectWithValue }) => {
    try {
      const response = await sosAPI.updateLocation(sosId, location);
      return response.data.sosRequest;
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || 'Failed to update SOS location',
      });
    }
  }
);

const sosSlice = createSlice({
  name: 'sos',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentLocation: (state, action: PayloadAction<LocationData>) => {
      state.currentLocation = action.payload;
    },
    setIsTracking: (state, action: PayloadAction<boolean>) => {
      state.isTracking = action.payload;
    },
    updateActiveSOS: (state, action: PayloadAction<Partial<SOSRequest>>) => {
      if (state.activeSOS) {
        state.activeSOS = { ...state.activeSOS, ...action.payload };
      }
    },
    setNearbyActivists: (state, action: PayloadAction<any[]>) => {
      state.nearbyActivists = action.payload;
    },
    clearActiveSOS: (state) => {
      state.activeSOS = null;
      state.isTracking = false;
    },
    // Real-time updates from socket
    sosAccepted: (state, action: PayloadAction<SOSRequest>) => {
      if (state.activeSOS && state.activeSOS.id === action.payload.id) {
        state.activeSOS = action.payload;
      }
    },
    sosResolved: (state, action: PayloadAction<SOSRequest>) => {
      if (state.activeSOS && state.activeSOS.id === action.payload.id) {
        state.activeSOS = action.payload;
        state.isTracking = false;
      }
    },
    newSOSRequest: (state, action: PayloadAction<{ sosRequest: SOSRequest; distance: number }>) => {
      // For activists - new SOS request nearby
      const { sosRequest } = action.payload;
      state.nearbyActivists.push(sosRequest);
    },
  },
  extraReducers: (builder) => {
    // Create SOS
    builder
      .addCase(createSOS.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createSOS.fulfilled, (state, action) => {
        state.isCreating = false;
        state.activeSOS = action.payload;
        state.isTracking = true;
      })
      .addCase(createSOS.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.payload?.message || 'Failed to create SOS request';
      });

    // Fetch SOS history
    builder
      .addCase(fetchSOSHistory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSOSHistory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.sosHistory = action.payload.sosRequests;
      })
      .addCase(fetchSOSHistory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || 'Failed to fetch SOS history';
      });

    // Accept SOS
    builder
      .addCase(acceptSOS.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(acceptSOS.fulfilled, (state, action) => {
        state.isLoading = false;
        // Update the SOS in nearby activists list
        const index = state.nearbyActivists.findIndex(sos => sos.id === action.payload.id);
        if (index !== -1) {
          state.nearbyActivists[index] = action.payload;
        }
      })
      .addCase(acceptSOS.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || 'Failed to accept SOS request';
      });

    // Resolve SOS
    builder
      .addCase(resolveSOS.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(resolveSOS.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.activeSOS && state.activeSOS.id === action.payload.id) {
          state.activeSOS = action.payload;
          state.isTracking = false;
        }
        // Add to history
        const existingIndex = state.sosHistory.findIndex(sos => sos.id === action.payload.id);
        if (existingIndex !== -1) {
          state.sosHistory[existingIndex] = action.payload;
        } else {
          state.sosHistory.unshift(action.payload);
        }
      })
      .addCase(resolveSOS.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || 'Failed to resolve SOS request';
      });

    // Cancel SOS
    builder
      .addCase(cancelSOS.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(cancelSOS.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.activeSOS && state.activeSOS.id === action.payload.id) {
          state.activeSOS = null;
          state.isTracking = false;
        }
        // Add to history
        const existingIndex = state.sosHistory.findIndex(sos => sos.id === action.payload.id);
        if (existingIndex !== -1) {
          state.sosHistory[existingIndex] = action.payload;
        } else {
          state.sosHistory.unshift(action.payload);
        }
      })
      .addCase(cancelSOS.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || 'Failed to cancel SOS request';
      });

    // Update SOS location
    builder
      .addCase(updateSOSLocation.fulfilled, (state, action) => {
        if (state.activeSOS && state.activeSOS.id === action.payload.id) {
          state.activeSOS = action.payload;
        }
      })
      .addCase(updateSOSLocation.rejected, (state, action) => {
        state.error = action.payload?.message || 'Failed to update SOS location';
      });
  },
});

export const {
  clearError,
  setCurrentLocation,
  setIsTracking,
  updateActiveSOS,
  setNearbyActivists,
  clearActiveSOS,
  sosAccepted,
  sosResolved,
  newSOSRequest,
} = sosSlice.actions;

export default sosSlice.reducer;
