import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types
interface SOSRequest {
  id: string;
  userId: string;
  location: {
    latitude: number;
    longitude: number;
  };
  status: 'active' | 'resolved' | 'cancelled';
  createdAt: string;
  updatedAt: string;
  message?: string;
  emergencyContacts?: string[];
}

interface SOSState {
  currentRequest: SOSRequest | null;
  requests: SOSRequest[];
  isLoading: boolean;
  error: string | null;
  nearbyActivists: any[];
}

const initialState: SOSState = {
  currentRequest: null,
  requests: [],
  isLoading: false,
  error: null,
  nearbyActivists: [],
};

// Async thunks
export const createSOSRequest = createAsyncThunk(
  'sos/createRequest',
  async (requestData: {
    userId: string;
    location: { latitude: number; longitude: number };
    message?: string;
  }) => {
    // Mock API call - replace with actual API
    const response = await new Promise<SOSRequest>((resolve) => {
      setTimeout(() => {
        resolve({
          id: Date.now().toString(),
          userId: requestData.userId,
          location: requestData.location,
          status: 'active',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          message: requestData.message,
          emergencyContacts: [],
        });
      }, 1000);
    });
    return response;
  }
);

export const updateSOSRequest = createAsyncThunk(
  'sos/updateRequest',
  async (updateData: { id: string; status: 'active' | 'resolved' | 'cancelled' }) => {
    // Mock API call - replace with actual API
    const response = await new Promise<SOSRequest>((resolve) => {
      setTimeout(() => {
        resolve({
          id: updateData.id,
          userId: 'mock-user',
          location: { latitude: 0, longitude: 0 },
          status: updateData.status,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      }, 500);
    });
    return response;
  }
);

// Slice
const sosSlice = createSlice({
  name: 'sos',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setNearbyActivists: (state, action: PayloadAction<any[]>) => {
      state.nearbyActivists = action.payload;
    },
    sosAccepted: (state, action: PayloadAction<{ requestId: string; activistId: string }>) => {
      if (state.currentRequest?.id === action.payload.requestId) {
        state.currentRequest.status = 'active';
      }
    },
    sosResolved: (state, action: PayloadAction<{ requestId: string }>) => {
      if (state.currentRequest?.id === action.payload.requestId) {
        state.currentRequest.status = 'resolved';
      }
    },
    newSOSRequest: (state, action: PayloadAction<SOSRequest>) => {
      state.requests.unshift(action.payload);
    },
  },
  extraReducers: (builder) => {
    builder
      // Create SOS Request
      .addCase(createSOSRequest.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createSOSRequest.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentRequest = action.payload;
        state.requests.unshift(action.payload);
      })
      .addCase(createSOSRequest.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to create SOS request';
      })
      // Update SOS Request
      .addCase(updateSOSRequest.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateSOSRequest.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.currentRequest?.id === action.payload.id) {
          state.currentRequest = action.payload;
        }
        const index = state.requests.findIndex(req => req.id === action.payload.id);
        if (index !== -1) {
          state.requests[index] = action.payload;
        }
      })
      .addCase(updateSOSRequest.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to update SOS request';
      });
  },
});

export const { clearError, setNearbyActivists, sosAccepted, sosResolved, newSOSRequest } = sosSlice.actions;
export default sosSlice.reducer;
