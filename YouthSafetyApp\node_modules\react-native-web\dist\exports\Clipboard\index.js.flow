/**
 * Copyright (c) <PERSON>.
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

'use client';

let clipboardAvailable;
declare export default class Clipboard {
  static isAvailable(): boolean,
  static getString(): Promise<string>,
  static setString(text: string): boolean,
}