import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  BaseUser, 
  YouthUser,
  ActivistUser,
  AdminUser,
  SOSRequest, 
  Post, 
  Confession, 
  LoginCredentials, 
  RegisterData,
  AuthTokens,
  ApiResponse,
  PaginatedResponse,
  EmergencyContact,
  MedicalInfo,
  Event,
  Notification,
  AnalyticsData,
  FileUpload
} from '@types/index';

// API Configuration
const API_CONFIG = {
  baseURL: __DEV__ 
    ? 'http://localhost:5000/api' 
    : 'https://your-production-api.com/api',
  timeout: 15000,
  retryAttempts: 3,
  retryDelay: 1000,
};

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = API_CONFIG.baseURL;
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: API_CONFIG.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token and handle retries
    this.api.interceptors.request.use(
      async (config) => {
        const token = await AsyncStorage.getItem('authToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        
        // Add request timestamp for debugging
        config.metadata = { startTime: new Date() };
        
        return config;
      },
      (error) => {
        console.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle token refresh and errors
    this.api.interceptors.response.use(
      (response) => {
        // Log response time in development
        if (__DEV__ && response.config.metadata) {
          const endTime = new Date();
          const duration = endTime.getTime() - response.config.metadata.startTime.getTime();
          console.log(`API ${response.config.method?.toUpperCase()} ${response.config.url}: ${duration}ms`);
        }
        return response;
      },
      async (error: AxiosError) => {
        const originalRequest = error.config as any;
        
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          try {
            const refreshToken = await AsyncStorage.getItem('refreshToken');
            if (refreshToken) {
              const response = await this.refreshToken(refreshToken);
              const { accessToken } = response.data.data;
              
              await AsyncStorage.setItem('authToken', accessToken);
              originalRequest.headers.Authorization = `Bearer ${accessToken}`;
              
              return this.api(originalRequest);
            }
          } catch (refreshError) {
            // Refresh failed, clear tokens and redirect to login
            await AsyncStorage.multiRemove(['authToken', 'refreshToken', 'user']);
            // Emit event for app-wide logout handling
            this.emitLogoutEvent();
          }
        }
        
        // Handle network errors with retry logic
        if (error.code === 'NETWORK_ERROR' || error.code === 'ECONNABORTED') {
          return this.retryRequest(originalRequest);
        }
        
        return Promise.reject(this.formatError(error));
      }
    );
  }

  private async retryRequest(config: any, attempt = 1): Promise<any> {
    if (attempt > API_CONFIG.retryAttempts) {
      throw new Error('Network request failed after maximum retry attempts');
    }

    await new Promise(resolve => setTimeout(resolve, API_CONFIG.retryDelay * attempt));
    
    try {
      return await this.api(config);
    } catch (error) {
      return this.retryRequest(config, attempt + 1);
    }
  }

  private formatError(error: AxiosError): Error {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.message || error.response.statusText || 'Server error';
      return new Error(`${error.response.status}: ${message}`);
    } else if (error.request) {
      // Network error
      return new Error('Network error: Please check your internet connection');
    } else {
      // Request setup error
      return new Error(`Request error: ${error.message}`);
    }
  }

  private emitLogoutEvent() {
    // In a real app, you might use an event emitter or Redux action
    console.log('User logged out due to token expiration');
  }

  // Auth endpoints
  async login(credentials: LoginCredentials): Promise<AxiosResponse<ApiResponse<{ user: BaseUser; tokens: AuthTokens }>>> {
    return this.api.post('/auth/login', credentials);
  }

  async register(data: RegisterData): Promise<AxiosResponse<ApiResponse<{ user: BaseUser; tokens: AuthTokens }>>> {
    return this.api.post('/auth/register', data);
  }

  async logout(): Promise<AxiosResponse<ApiResponse<void>>> {
    return this.api.post('/auth/logout');
  }

  async refreshToken(refreshToken: string): Promise<AxiosResponse<ApiResponse<AuthTokens>>> {
    return this.api.post('/auth/refresh', { refreshToken });
  }

  async forgotPassword(email: string): Promise<AxiosResponse<ApiResponse<void>>> {
    return this.api.post('/auth/forgot-password', { email });
  }

  async resetPassword(token: string, password: string): Promise<AxiosResponse<ApiResponse<void>>> {
    return this.api.post('/auth/reset-password', { token, password });
  }

  async verifyEmail(token: string): Promise<AxiosResponse<ApiResponse<void>>> {
    return this.api.post('/auth/verify-email', { token });
  }

  async resendVerification(): Promise<AxiosResponse<ApiResponse<void>>> {
    return this.api.post('/auth/resend-verification');
  }

  // User endpoints
  async getProfile(): Promise<AxiosResponse<ApiResponse<BaseUser>>> {
    return this.api.get('/users/profile');
  }

  async updateProfile(data: Partial<BaseUser>): Promise<AxiosResponse<ApiResponse<BaseUser>>> {
    return this.api.put('/users/profile', data);
  }

  async updatePreferences(preferences: any): Promise<AxiosResponse<ApiResponse<void>>> {
    return this.api.put('/users/preferences', preferences);
  }

  async uploadAvatar(file: FormData): Promise<AxiosResponse<ApiResponse<{ url: string }>>> {
    return this.api.post('/users/avatar', file, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  async deleteAccount(): Promise<AxiosResponse<ApiResponse<void>>> {
    return this.api.delete('/users/account');
  }

  // Emergency Contacts
  async getEmergencyContacts(): Promise<AxiosResponse<ApiResponse<EmergencyContact[]>>> {
    return this.api.get('/users/emergency-contacts');
  }

  async addEmergencyContact(contact: Omit<EmergencyContact, 'id' | 'createdAt'>): Promise<AxiosResponse<ApiResponse<EmergencyContact>>> {
    return this.api.post('/users/emergency-contacts', contact);
  }

  async updateEmergencyContact(id: string, contact: Partial<EmergencyContact>): Promise<AxiosResponse<ApiResponse<EmergencyContact>>> {
    return this.api.put(`/users/emergency-contacts/${id}`, contact);
  }

  async deleteEmergencyContact(id: string): Promise<AxiosResponse<ApiResponse<void>>> {
    return this.api.delete(`/users/emergency-contacts/${id}`);
  }

  // Medical Info
  async getMedicalInfo(): Promise<AxiosResponse<ApiResponse<MedicalInfo>>> {
    return this.api.get('/users/medical-info');
  }

  async updateMedicalInfo(medicalInfo: Partial<MedicalInfo>): Promise<AxiosResponse<ApiResponse<MedicalInfo>>> {
    return this.api.put('/users/medical-info', medicalInfo);
  }

  // SOS endpoints
  async createSOS(data: Partial<SOSRequest>): Promise<AxiosResponse<ApiResponse<{ sosRequest: SOSRequest }>>> {
    return this.api.post('/sos', data);
  }

  async getSOSById(id: string): Promise<AxiosResponse<ApiResponse<{ sosRequest: SOSRequest }>>> {
    return this.api.get(`/sos/${id}`);
  }

  async updateSOSLocation(id: string, location: { latitude: number; longitude: number }): Promise<AxiosResponse<ApiResponse<{ sosRequest: SOSRequest }>>> {
    return this.api.put(`/sos/${id}/location`, location);
  }

  async acceptSOS(id: string): Promise<AxiosResponse<ApiResponse<{ sosRequest: SOSRequest }>>> {
    return this.api.post(`/sos/${id}/accept`);
  }

  async resolveSOS(id: string, notes?: string): Promise<AxiosResponse<ApiResponse<{ sosRequest: SOSRequest }>>> {
    return this.api.post(`/sos/${id}/resolve`, { notes });
  }

  async cancelSOS(id: string, reason?: string): Promise<AxiosResponse<ApiResponse<{ sosRequest: SOSRequest }>>> {
    return this.api.post(`/sos/${id}/cancel`, { reason });
  }

  async getMySOSRequests(page = 1, limit = 10): Promise<AxiosResponse<ApiResponse<PaginatedResponse<SOSRequest>>>> {
    return this.api.get(`/sos/my-requests?page=${page}&limit=${limit}`);
  }

  // Posts endpoints
  async createPost(data: Partial<Post>): Promise<AxiosResponse<ApiResponse<{ post: Post }>>> {
    return this.api.post('/posts', data);
  }

  async getPosts(page = 1, limit = 20, category?: string): Promise<AxiosResponse<ApiResponse<PaginatedResponse<Post>>>> {
    const params = new URLSearchParams({ page: page.toString(), limit: limit.toString() });
    if (category) params.append('category', category);
    return this.api.get(`/posts?${params.toString()}`);
  }

  async getPostById(id: string): Promise<AxiosResponse<ApiResponse<{ post: Post }>>> {
    return this.api.get(`/posts/${id}`);
  }

  async reactToPost(id: string, type: 'like' | 'love' | 'support' | 'care'): Promise<AxiosResponse<ApiResponse<void>>> {
    return this.api.post(`/posts/${id}/react`, { type });
  }

  async commentOnPost(id: string, content: string): Promise<AxiosResponse<ApiResponse<void>>> {
    return this.api.post(`/posts/${id}/comment`, { content });
  }

  async reportPost(id: string, reason: string): Promise<AxiosResponse<ApiResponse<void>>> {
    return this.api.post(`/posts/${id}/report`, { reason });
  }

  // Confessions endpoints
  async createConfession(data: Partial<Confession>): Promise<AxiosResponse<ApiResponse<{ confession: Confession }>>> {
    return this.api.post('/confessions', data);
  }

  async getConfessions(page = 1, limit = 20, category?: string): Promise<AxiosResponse<ApiResponse<PaginatedResponse<Confession>>>> {
    const params = new URLSearchParams({ page: page.toString(), limit: limit.toString() });
    if (category) params.append('category', category);
    return this.api.get(`/confessions?${params.toString()}`);
  }

  async respondToConfession(id: string, content: string, isAnonymous = true): Promise<AxiosResponse<ApiResponse<void>>> {
    return this.api.post(`/confessions/${id}/respond`, { content, isAnonymous });
  }

  async reportConfession(id: string, reason: string): Promise<AxiosResponse<ApiResponse<void>>> {
    return this.api.post(`/confessions/${id}/report`, { reason });
  }
}

export const apiService = new ApiService();

// Individual API services for better organization
export const authAPI = {
  login: (credentials: LoginCredentials) => apiService.login(credentials),
  register: (data: RegisterData) => apiService.register(data),
  logout: () => apiService.logout(),
  refreshToken: (refreshToken: string) => apiService.refreshToken(refreshToken),
  forgotPassword: (email: string) => apiService.forgotPassword(email),
  resetPassword: (token: string, password: string) => apiService.resetPassword(token, password),
  verifyEmail: (token: string) => apiService.verifyEmail(token),
  resendVerification: () => apiService.resendVerification(),
};

export const usersAPI = {
  getProfile: () => apiService.getProfile(),
  updateProfile: (data: Partial<BaseUser>) => apiService.updateProfile(data),
  updatePreferences: (preferences: any) => apiService.updatePreferences(preferences),
  uploadAvatar: (file: FormData) => apiService.uploadAvatar(file),
  deleteAccount: () => apiService.deleteAccount(),

  // Emergency Contacts
  getEmergencyContacts: () => apiService.getEmergencyContacts(),
  addEmergencyContact: (contact: Omit<EmergencyContact, 'id' | 'createdAt'>) =>
    apiService.addEmergencyContact(contact),
  updateEmergencyContact: (id: string, contact: Partial<EmergencyContact>) =>
    apiService.updateEmergencyContact(id, contact),
  deleteEmergencyContact: (id: string) => apiService.deleteEmergencyContact(id),

  // Medical Info
  getMedicalInfo: () => apiService.getMedicalInfo(),
  updateMedicalInfo: (medicalInfo: Partial<MedicalInfo>) =>
    apiService.updateMedicalInfo(medicalInfo),
};

export const sosAPI = {
  create: (data: Partial<SOSRequest>) => apiService.createSOS(data),
  getById: (id: string) => apiService.getSOSById(id),
  updateLocation: (id: string, location: { latitude: number; longitude: number }) =>
    apiService.updateSOSLocation(id, location),
  accept: (id: string) => apiService.acceptSOS(id),
  resolve: (id: string, notes?: string) => apiService.resolveSOS(id, notes),
  cancel: (id: string, reason?: string) => apiService.cancelSOS(id, reason),
  getMyRequests: (page?: number, limit?: number) =>
    apiService.getMySOSRequests(page, limit),
};

export const postsAPI = {
  create: (data: Partial<Post>) => apiService.createPost(data),
  getFeed: (page?: number, limit?: number, category?: string) =>
    apiService.getPosts(page, limit, category),
  getById: (id: string) => apiService.getPostById(id),
  react: (id: string, type: 'like' | 'love' | 'support' | 'care') =>
    apiService.reactToPost(id, type),
  comment: (id: string, content: string) => apiService.commentOnPost(id, content),
  report: (id: string, reason: string) => apiService.reportPost(id, reason),
};

export const confessionsAPI = {
  create: (data: Partial<Confession>) => apiService.createConfession(data),
  getAll: (page?: number, limit?: number, category?: string) =>
    apiService.getConfessions(page, limit, category),
  respond: (id: string, content: string, isAnonymous?: boolean) =>
    apiService.respondToConfession(id, content, isAnonymous),
  report: (id: string, reason: string) => apiService.reportConfession(id, reason),
};

// Admin API (will be extended)
export const adminAPI = {
  getUsers: async (page = 1, limit = 20, role?: string) => {
    const params = new URLSearchParams({ page: page.toString(), limit: limit.toString() });
    if (role) params.append('role', role);
    return apiService.api.get(`/admin/users?${params.toString()}`);
  },

  verifyActivist: (userId: string, status: 'verified' | 'rejected') =>
    apiService.api.post(`/admin/verify-activist/${userId}`, { status }),

  getAnalytics: () => apiService.api.get('/admin/analytics'),

  moderateContent: (contentId: string, contentType: 'post' | 'confession', action: 'approve' | 'reject') =>
    apiService.api.post('/admin/moderate', { contentId, contentType, action }),
};

// Export default instance
export default apiService;
