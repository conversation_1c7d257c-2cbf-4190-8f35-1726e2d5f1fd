import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { ActivistUser, SOSRequest } from '@types/index';
import { useAppSelector, useAppDispatch } from '@/store';
import { socketService } from '@/services/socketService';
import { locationService } from '@/services/locationService';

const { width } = Dimensions.get('window');

interface ActivistDashboardProps {
  user: ActivistUser;
}

const ActivistDashboard: React.FC<ActivistDashboardProps> = ({ user }) => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  
  const [isOnline, setIsOnline] = useState(user.isOnline);
  const [nearbySOSRequests, setNearbySOSRequests] = useState<SOSRequest[]>([]);
  const [activeSOSCount, setActiveSOSCount] = useState(0);
  const [responseStats, setResponseStats] = useState({
    totalResponses: 0,
    averageResponseTime: 0,
    successRate: 0,
  });

  useEffect(() => {
    loadDashboardData();
    setupSocketListeners();
    
    if (isOnline) {
      startLocationSharing();
    }

    return () => {
      stopLocationSharing();
    };
  }, [isOnline]);

  const loadDashboardData = async () => {
    try {
      // Load nearby SOS requests, stats, etc.
      // This would be implemented with actual API calls
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  };

  const setupSocketListeners = () => {
    // Listen for new SOS requests
    socketService.socket?.on('new_sos_request', (data) => {
      setNearbySOSRequests(prev => [data.sosRequest, ...prev]);
      
      // Show notification
      Alert.alert(
        'New Emergency Request',
        `${data.sosRequest.emergency_type} emergency ${data.distance.toFixed(1)}km away`,
        [
          { text: 'Ignore', style: 'cancel' },
          { text: 'View Details', onPress: () => viewSOSDetails(data.sosRequest) },
        ]
      );
    });
  };

  const toggleOnlineStatus = async (value: boolean) => {
    try {
      setIsOnline(value);
      
      if (value) {
        await startLocationSharing();
        socketService.updateUserStatus('online');
      } else {
        await stopLocationSharing();
        socketService.updateUserStatus('offline');
      }
      
      // Update user status in backend
      // await activistAPI.updateStatus(value ? 'online' : 'offline');
    } catch (error) {
      console.error('Error updating online status:', error);
      Alert.alert('Error', 'Failed to update status');
      setIsOnline(!value); // Revert on error
    }
  };

  const startLocationSharing = async () => {
    try {
      await locationService.startLocationTracking(
        (location) => {
          // Share location with socket for SOS matching
          socketService.shareLocation({
            latitude: location.latitude,
            longitude: location.longitude,
          });
        },
        {
          timeInterval: 30000, // Update every 30 seconds
          distanceInterval: 100, // Update every 100 meters
        }
      );
    } catch (error) {
      console.error('Error starting location sharing:', error);
    }
  };

  const stopLocationSharing = async () => {
    await locationService.stopLocationTracking();
  };

  const viewSOSDetails = (sosRequest: SOSRequest) => {
    navigation.navigate('SOSDetails', { sosRequest } as never);
  };

  const acceptSOSRequest = async (sosId: string) => {
    try {
      // Accept SOS request
      // await sosAPI.accept(sosId);
      
      // Navigate to SOS response screen
      navigation.navigate('SOSResponse', { sosId } as never);
    } catch (error) {
      console.error('Error accepting SOS:', error);
      Alert.alert('Error', 'Failed to accept SOS request');
    }
  };

  const navigateToProfile = () => {
    navigation.navigate('ActivistProfile' as never);
  };

  const navigateToEvents = () => {
    navigation.navigate('EventManagement' as never);
  };

  const navigateToKnowledgeHub = () => {
    navigation.navigate('KnowledgeHub' as never);
  };

  const navigateToAnalytics = () => {
    navigation.navigate('ActivistAnalytics' as never);
  };

  const getVerificationStatusColor = () => {
    switch (user.verificationStatus) {
      case 'verified': return '#10b981';
      case 'pending': return '#f59e0b';
      case 'suspended': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getVerificationStatusText = () => {
    switch (user.verificationStatus) {
      case 'verified': return 'Verified Activist';
      case 'pending': return 'Verification Pending';
      case 'suspended': return 'Account Suspended';
      default: return 'Unknown Status';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>Hello, {user.firstName}!</Text>
            <View style={styles.statusContainer}>
              <View 
                style={[
                  styles.statusIndicator, 
                  { backgroundColor: getVerificationStatusColor() }
                ]} 
              />
              <Text style={styles.statusText}>{getVerificationStatusText()}</Text>
            </View>
          </View>
          <TouchableOpacity onPress={navigateToProfile}>
            <Ionicons name="person-circle-outline" size={32} color="#6366f1" />
          </TouchableOpacity>
        </View>

        {/* Online Status Toggle */}
        <View style={styles.onlineStatusCard}>
          <View style={styles.onlineStatusHeader}>
            <View>
              <Text style={styles.onlineStatusTitle}>Availability Status</Text>
              <Text style={styles.onlineStatusSubtitle}>
                {isOnline ? 'Available for emergency responses' : 'Currently offline'}
              </Text>
            </View>
            <Switch
              value={isOnline}
              onValueChange={toggleOnlineStatus}
              trackColor={{ false: '#e5e7eb', true: '#10b981' }}
              thumbColor={isOnline ? '#ffffff' : '#f3f4f6'}
              disabled={user.verificationStatus !== 'verified'}
            />
          </View>
          
          {user.verificationStatus !== 'verified' && (
            <View style={styles.verificationNotice}>
              <Ionicons name="information-circle" size={16} color="#f59e0b" />
              <Text style={styles.verificationNoticeText}>
                Complete verification to respond to emergencies
              </Text>
            </View>
          )}
        </View>

        {/* Quick Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{activeSOSCount}</Text>
            <Text style={styles.statLabel}>Active Emergencies</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{responseStats.totalResponses}</Text>
            <Text style={styles.statLabel}>Total Responses</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{user.rating.toFixed(1)}</Text>
            <Text style={styles.statLabel}>Rating</Text>
          </View>
        </View>

        {/* Nearby SOS Requests */}
        {isOnline && nearbySOSRequests.length > 0 && (
          <View style={styles.sosSection}>
            <Text style={styles.sectionTitle}>Nearby Emergency Requests</Text>
            {nearbySOSRequests.slice(0, 3).map((sos) => (
              <View key={sos.id} style={styles.sosCard}>
                <View style={styles.sosHeader}>
                  <View style={styles.sosTypeContainer}>
                    <Ionicons 
                      name="warning" 
                      size={20} 
                      color="#ef4444" 
                    />
                    <Text style={styles.sosType}>
                      {sos.emergencyType.replace('_', ' ').toUpperCase()}
                    </Text>
                  </View>
                  <Text style={styles.sosTime}>
                    {new Date(sos.timestamp).toLocaleTimeString()}
                  </Text>
                </View>
                
                <Text style={styles.sosLocation}>{sos.address}</Text>
                
                {sos.description && (
                  <Text style={styles.sosDescription}>{sos.description}</Text>
                )}
                
                <View style={styles.sosActions}>
                  <TouchableOpacity
                    style={styles.viewDetailsButton}
                    onPress={() => viewSOSDetails(sos)}
                  >
                    <Text style={styles.viewDetailsButtonText}>View Details</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.acceptButton}
                    onPress={() => acceptSOSRequest(sos.id)}
                  >
                    <Text style={styles.acceptButtonText}>Accept</Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </View>
        )}

        {/* Quick Actions */}
        <View style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity 
              style={styles.quickActionCard}
              onPress={navigateToEvents}
            >
              <Ionicons name="calendar" size={24} color="#6366f1" />
              <Text style={styles.quickActionTitle}>Events</Text>
              <Text style={styles.quickActionSubtitle}>Manage community events</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.quickActionCard}
              onPress={navigateToKnowledgeHub}
            >
              <Ionicons name="library" size={24} color="#10b981" />
              <Text style={styles.quickActionTitle}>Knowledge Hub</Text>
              <Text style={styles.quickActionSubtitle}>Share resources</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.quickActionCard}
              onPress={navigateToAnalytics}
            >
              <Ionicons name="analytics" size={24} color="#f59e0b" />
              <Text style={styles.quickActionTitle}>Analytics</Text>
              <Text style={styles.quickActionSubtitle}>View performance</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.quickActionCard}
              onPress={() => navigation.navigate('Training' as never)}
            >
              <Ionicons name="school" size={24} color="#8b5cf6" />
              <Text style={styles.quickActionTitle}>Training</Text>
              <Text style={styles.quickActionSubtitle}>Skill development</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.recentActivitySection}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          <View style={styles.activityCard}>
            <Text style={styles.activityText}>No recent activity</Text>
            <Text style={styles.activitySubtext}>
              Your recent emergency responses and community activities will appear here
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    color: '#6b7280',
  },
  onlineStatusCard: {
    backgroundColor: '#ffffff',
    margin: 16,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  onlineStatusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  onlineStatusTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  onlineStatusSubtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  verificationNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    padding: 12,
    backgroundColor: '#fef3c7',
    borderRadius: 8,
  },
  verificationNoticeText: {
    fontSize: 14,
    color: '#92400e',
    marginLeft: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
    textAlign: 'center',
  },
  sosSection: {
    margin: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  sosCard: {
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#ef4444',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sosHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  sosTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sosType: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ef4444',
    marginLeft: 8,
  },
  sosTime: {
    fontSize: 12,
    color: '#6b7280',
  },
  sosLocation: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 8,
  },
  sosDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 12,
  },
  sosActions: {
    flexDirection: 'row',
    gap: 12,
  },
  viewDetailsButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#6366f1',
    alignItems: 'center',
  },
  viewDetailsButtonText: {
    color: '#6366f1',
    fontSize: 14,
    fontWeight: '500',
  },
  acceptButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: '#ef4444',
    alignItems: 'center',
  },
  acceptButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
  },
  quickActionsSection: {
    margin: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickActionCard: {
    width: (width - 44) / 2,
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  quickActionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginTop: 8,
  },
  quickActionSubtitle: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
    textAlign: 'center',
  },
  recentActivitySection: {
    margin: 16,
    marginBottom: 32,
  },
  activityCard: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  activityText: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 8,
  },
  activitySubtext: {
    fontSize: 14,
    color: '#9ca3af',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default ActivistDashboard;
