import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { RootStackParamList } from '@types/index';

// Import youth screens
import YouthDashboardScreen from '@screens/youth/YouthDashboardScreen';
import SocialFeedScreen from '@screens/social/SocialFeedScreen';
import ConfessionsRoomScreen from '@screens/social/ConfessionsRoomScreen';
import ProfileScreen from '@screens/profile/ProfileScreen';
import SOSTrackingScreen from '@screens/sos/SOSTrackingScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator<RootStackParamList>();

const YouthTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Dashboard') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Social') {
            iconName = focused ? 'people' : 'people-outline';
          } else if (route.name === 'Confessions') {
            iconName = focused ? 'chatbubbles' : 'chatbubbles-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#007AFF',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={YouthDashboardScreen}
        options={{ title: 'Home' }}
      />
      <Tab.Screen 
        name="Social" 
        component={SocialFeedScreen}
        options={{ title: 'Social' }}
      />
      <Tab.Screen 
        name="Confessions" 
        component={ConfessionsRoomScreen}
        options={{ title: 'Confessions' }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{ title: 'Profile' }}
      />
    </Tab.Navigator>
  );
};

const YouthNavigator: React.FC = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="YouthTabs" component={YouthTabNavigator} />
      <Stack.Screen 
        name="SOSTracking" 
        component={SOSTrackingScreen}
        options={{ 
          presentation: 'modal',
          gestureEnabled: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default YouthNavigator;
