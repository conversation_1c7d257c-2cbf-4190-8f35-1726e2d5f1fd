import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import MapView, { <PERSON><PERSON>, Circle } from 'react-native-maps';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useAppSelector, useAppDispatch } from '@/store';
import { locationService } from '@/services/locationService';
import { sosAPI } from '@/services/api';
import { SOSRequest, LocationData } from '@types/index';

const { width, height } = Dimensions.get('window');

interface SOSTrackingScreenProps {
  route: {
    params: {
      sosRequest: SOSRequest;
    };
  };
}

const SOSTrackingScreen: React.FC<SOSTrackingScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useAppDispatch();
  
  const { user } = useAppSelector((state) => state.auth);
  const [sosRequest, setSosRequest] = useState<SOSRequest>(route.params?.sosRequest);
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null);
  const [isTracking, setIsTracking] = useState(true);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [activistETA, setActivistETA] = useState<number | null>(null);
  
  const mapRef = useRef<MapView>(null);
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    startLocationTracking();
    startTimer();
    startPulseAnimation();
    
    return () => {
      stopLocationTracking();
      stopTimer();
    };
  }, []);

  useEffect(() => {
    if (currentLocation && sosRequest) {
      updateSOSLocation();
    }
  }, [currentLocation]);

  const startLocationTracking = async () => {
    try {
      await locationService.startLocationTracking(
        (location) => {
          setCurrentLocation(location);
          
          // Center map on current location
          if (mapRef.current) {
            mapRef.current.animateToRegion({
              latitude: location.latitude,
              longitude: location.longitude,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            });
          }
        },
        {
          timeInterval: 3000, // Update every 3 seconds during emergency
          distanceInterval: 5, // Update every 5 meters
        }
      );
    } catch (error) {
      console.error('Error starting location tracking:', error);
      Alert.alert('Error', 'Unable to start location tracking');
    }
  };

  const stopLocationTracking = async () => {
    await locationService.stopLocationTracking();
    setIsTracking(false);
  };

  const startTimer = () => {
    timerRef.current = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);
  };

  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const updateSOSLocation = async () => {
    if (!currentLocation || !sosRequest) return;

    try {
      await sosAPI.updateLocation(sosRequest.id, {
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
      });
    } catch (error) {
      console.error('Error updating SOS location:', error);
    }
  };

  const handleCancelSOS = () => {
    Alert.alert(
      'Cancel SOS',
      'Are you sure you want to cancel this emergency request?',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: cancelSOS,
        },
      ]
    );
  };

  const cancelSOS = async () => {
    try {
      await sosAPI.cancel(sosRequest.id, 'Cancelled by user');
      await stopLocationTracking();
      navigation.goBack();
    } catch (error) {
      console.error('Error cancelling SOS:', error);
      Alert.alert('Error', 'Unable to cancel SOS request');
    }
  };

  const handleResolveSOS = () => {
    Alert.alert(
      'Mark as Resolved',
      'Are you safe now? This will mark the emergency as resolved.',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, I\'m Safe',
          onPress: resolveSOS,
        },
      ]
    );
  };

  const resolveSOS = async () => {
    try {
      await sosAPI.resolve(sosRequest.id, 'Resolved by user - safe now');
      await stopLocationTracking();
      
      Alert.alert(
        'SOS Resolved',
        'Your emergency has been marked as resolved. Stay safe!',
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate('YouthDashboard' as never),
          },
        ]
      );
    } catch (error) {
      console.error('Error resolving SOS:', error);
      Alert.alert('Error', 'Unable to resolve SOS request');
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusColor = () => {
    switch (sosRequest?.status) {
      case 'active':
        return '#ef4444';
      case 'accepted':
        return '#f59e0b';
      case 'resolved':
        return '#10b981';
      default:
        return '#6b7280';
    }
  };

  const getStatusText = () => {
    switch (sosRequest?.status) {
      case 'active':
        return 'Searching for help...';
      case 'accepted':
        return 'Help is on the way!';
      case 'resolved':
        return 'Emergency resolved';
      default:
        return 'Unknown status';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.statusContainer}>
          <Animated.View
            style={[
              styles.statusIndicator,
              { backgroundColor: getStatusColor(), transform: [{ scale: pulseAnim }] },
            ]}
          />
          <View>
            <Text style={styles.statusText}>{getStatusText()}</Text>
            <Text style={styles.timeText}>Active for {formatTime(elapsedTime)}</Text>
          </View>
        </View>
        
        {activistETA && (
          <View style={styles.etaContainer}>
            <Ionicons name="time-outline" size={16} color="#6b7280" />
            <Text style={styles.etaText}>ETA: {activistETA} min</Text>
          </View>
        )}
      </View>

      {/* Map */}
      <View style={styles.mapContainer}>
        <MapView
          ref={mapRef}
          style={styles.map}
          showsUserLocation={true}
          showsMyLocationButton={false}
          followsUserLocation={true}
          initialRegion={{
            latitude: currentLocation?.latitude || 37.78825,
            longitude: currentLocation?.longitude || -122.4324,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          }}
        >
          {currentLocation && (
            <>
              <Marker
                coordinate={{
                  latitude: currentLocation.latitude,
                  longitude: currentLocation.longitude,
                }}
                title="Your Location"
                description="Current emergency location"
              >
                <View style={styles.emergencyMarker}>
                  <Ionicons name="warning" size={24} color="#ffffff" />
                </View>
              </Marker>
              
              <Circle
                center={{
                  latitude: currentLocation.latitude,
                  longitude: currentLocation.longitude,
                }}
                radius={currentLocation.accuracy || 50}
                fillColor="rgba(239, 68, 68, 0.2)"
                strokeColor="rgba(239, 68, 68, 0.5)"
                strokeWidth={2}
              />
            </>
          )}
        </MapView>
      </View>

      {/* Emergency Info */}
      <View style={styles.infoContainer}>
        <View style={styles.infoCard}>
          <View style={styles.infoRow}>
            <Ionicons name="location-outline" size={20} color="#6b7280" />
            <Text style={styles.infoText}>
              {currentLocation?.address || 'Getting location...'}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="medical-outline" size={20} color="#6b7280" />
            <Text style={styles.infoText}>
              Emergency Type: {sosRequest?.emergencyType?.replace('_', ' ').toUpperCase()}
            </Text>
          </View>
          
          {sosRequest?.description && (
            <View style={styles.infoRow}>
              <Ionicons name="document-text-outline" size={20} color="#6b7280" />
              <Text style={styles.infoText}>{sosRequest.description}</Text>
            </View>
          )}
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={[styles.actionButton, styles.resolveButton]}
          onPress={handleResolveSOS}
        >
          <Ionicons name="checkmark-circle" size={24} color="#ffffff" />
          <Text style={styles.actionButtonText}>I'm Safe</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, styles.cancelButton]}
          onPress={handleCancelSOS}
        >
          <Ionicons name="close-circle" size={24} color="#ffffff" />
          <Text style={styles.actionButtonText}>Cancel</Text>
        </TouchableOpacity>
      </View>

      {/* Emergency Contacts */}
      <View style={styles.emergencyContactsContainer}>
        <TouchableOpacity style={styles.emergencyContactButton}>
          <Ionicons name="call" size={20} color="#ef4444" />
          <Text style={styles.emergencyContactText}>Call Emergency Services</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  timeText: {
    fontSize: 14,
    color: '#6b7280',
  },
  etaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  etaText: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 4,
  },
  mapContainer: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  emergencyMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#ef4444',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  infoContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  infoCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#374151',
    marginLeft: 12,
    lineHeight: 20,
  },
  actionContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  resolveButton: {
    backgroundColor: '#10b981',
  },
  cancelButton: {
    backgroundColor: '#ef4444',
  },
  actionButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  emergencyContactsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  emergencyContactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ffffff',
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#ef4444',
  },
  emergencyContactText: {
    color: '#ef4444',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default SOSTrackingScreen;
