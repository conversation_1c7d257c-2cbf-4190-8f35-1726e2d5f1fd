{"name": "caf", "version": "15.0.1", "description": "Cancelable Async Flows: a wrapper to treat generators as cancelable async functions", "main": "./index.js", "browser": "./src/caf.js", "scripts": {"test": "node scripts/node-tests.js", "test:package": "TEST_PACKAGE=true npm test", "test:umd": "TEST_UMD=true npm test", "test:esm": "TEST_ESM=true npm test", "test:all": "npm test && npm run test:package && npm run test:umd && npm run test:esm", "coverage": "nyc -t './coverage/.nyc_output' --reporter=lcov --reporter=html --reporter=text --exclude=**/abortcontroller-polyfill-only.js --exclude=**/tests --exclude=**/node-tests.js node scripts/node-tests.js", "coverage:report": "npm run coverage && cat ./coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js", "build-all": "node scripts/build-all.js", "build": "npm run build-all", "prepare": "npm run build", "prepublish": "npm run build && npm run test:all", "publish": "npm run coverage:report"}, "devDependencies": {"abortcontroller-polyfill": "~1.7.3", "coveralls": "~3.1.1", "moduloze": "~0.9.1", "nyc": "~15.1.0", "qunit": "~2.19.1", "terser": "~5.15.0"}, "mz-dependencies": {"caf.js": "CAF", "cag.js": "CAG", "shared.js": "CAFShared"}, "exports": {".": {"import": "./dist/esm/index.mjs", "default": "./index.js"}, "./core": {"import": "./dist/esm/caf.mjs", "default": "./src/caf.js"}, "./caf": {"import": "./dist/esm/caf.mjs", "default": "./src/caf.js"}, "./cag": {"import": "./dist/esm/cag.mjs", "default": "./src/cag.js"}}, "repository": "getify/caf", "keywords": ["async", "promises", "generators", "cancelation"], "bugs": {"url": "https://github.com/getify/caf/issues", "email": "<EMAIL>"}, "homepage": "https://github.com/getify/caf", "author": "<PERSON> <<EMAIL>>", "license": "MIT"}