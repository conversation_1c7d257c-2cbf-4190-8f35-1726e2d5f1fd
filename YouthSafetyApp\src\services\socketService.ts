import { io, Socket } from 'socket.io-client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { store } from '@/store';
import { sosAccepted, sosResolved, newSOSRequest } from '@/store/slices/sosSlice';
import { addNotification } from '@/store/slices/notificationSlice';

class SocketService {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  async connect(): Promise<void> {
    try {
      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        console.log('No auth token found, skipping socket connection');
        return;
      }

      const socketURL = __DEV__ 
        ? 'http://localhost:5000' 
        : 'https://your-production-api.com';

      this.socket = io(socketURL, {
        auth: {
          token,
        },
        transports: ['websocket'],
        timeout: 10000,
      });

      this.setupEventListeners();
    } catch (error) {
      console.error('Error connecting to socket:', error);
    }
  }

  private setupEventListeners(): void {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      console.log('Socket connected');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      // Join user-specific room
      const state = store.getState();
      const userId = state.auth.user?.id;
      if (userId) {
        this.socket?.emit('join_user_room', userId);
      }
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      this.isConnected = false;
      
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.handleReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.handleReconnect();
    });

    // SOS-related events
    this.socket.on('new_sos_request', (data) => {
      console.log('New SOS request received:', data);
      store.dispatch(newSOSRequest(data));
      
      // Show notification for activists
      const state = store.getState();
      if (state.auth.user?.role === 'activist') {
        store.dispatch(addNotification({
          id: Date.now().toString(),
          type: 'sos_request',
          title: 'New Emergency Request',
          message: `Emergency ${data.sosRequest.emergency_type} request nearby`,
          data: data.sosRequest,
          timestamp: new Date(),
          isRead: false,
        }));
      }
    });

    this.socket.on('sos_accepted', (data) => {
      console.log('SOS accepted:', data);
      store.dispatch(sosAccepted(data.sosRequest));
      
      store.dispatch(addNotification({
        id: Date.now().toString(),
        type: 'sos_update',
        title: 'Help is Coming!',
        message: 'An activist has accepted your emergency request',
        data: data,
        timestamp: new Date(),
        isRead: false,
      }));
    });

    this.socket.on('sos_resolved', (data) => {
      console.log('SOS resolved:', data);
      store.dispatch(sosResolved(data.sosRequest));
      
      store.dispatch(addNotification({
        id: Date.now().toString(),
        type: 'sos_update',
        title: 'Emergency Resolved',
        message: 'Your emergency has been marked as resolved',
        data: data,
        timestamp: new Date(),
        isRead: false,
      }));
    });

    this.socket.on('sos_taken', (data) => {
      console.log('SOS taken by another activist:', data);
      // Remove from nearby activists list for other activists
    });

    // Location tracking events
    this.socket.on('location_update', (data) => {
      console.log('Location update received:', data);
      // Handle real-time location updates
    });

    // Chat/messaging events
    this.socket.on('new_message', (data) => {
      console.log('New message received:', data);
      // Handle incoming messages
    });

    // Video call events
    this.socket.on('incoming_call', (data) => {
      console.log('Incoming call:', data);
      // Handle incoming video call
    });

    this.socket.on('call_accepted', (data) => {
      console.log('Call accepted:', data);
      // Handle call acceptance
    });

    this.socket.on('call_rejected', (data) => {
      console.log('Call rejected:', data);
      // Handle call rejection
    });

    this.socket.on('call_ended', (data) => {
      console.log('Call ended:', data);
      // Handle call end
    });

    // WebRTC signaling events
    this.socket.on('webrtc_offer', (data) => {
      console.log('WebRTC offer received:', data);
      // Handle WebRTC offer
    });

    this.socket.on('webrtc_answer', (data) => {
      console.log('WebRTC answer received:', data);
      // Handle WebRTC answer
    });

    this.socket.on('webrtc_ice_candidate', (data) => {
      console.log('WebRTC ICE candidate received:', data);
      // Handle ICE candidate
    });
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Exponential backoff
      
      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
      
      setTimeout(() => {
        this.connect();
      }, delay);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  // SOS-related methods
  joinSOSRoom(sosId: string): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('join_sos_room', sosId);
    }
  }

  leaveSOSRoom(sosId: string): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('leave_sos_room', sosId);
    }
  }

  updateSOSLocation(sosId: string, location: { latitude: number; longitude: number }): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('update_sos_location', { sosId, location });
    }
  }

  // Messaging methods
  sendMessage(recipientId: string, message: string, messageType: 'text' | 'audio' | 'image' = 'text'): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('send_message', {
        recipientId,
        message,
        messageType,
        timestamp: new Date(),
      });
    }
  }

  // Video call methods
  initiateCall(recipientId: string, callType: 'audio' | 'video' = 'video'): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('initiate_call', {
        recipientId,
        callType,
        timestamp: new Date(),
      });
    }
  }

  acceptCall(callId: string): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('accept_call', { callId });
    }
  }

  rejectCall(callId: string): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('reject_call', { callId });
    }
  }

  endCall(callId: string): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('end_call', { callId });
    }
  }

  // WebRTC signaling methods
  sendWebRTCOffer(recipientId: string, offer: RTCSessionDescriptionInit): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('webrtc_offer', { recipientId, offer });
    }
  }

  sendWebRTCAnswer(recipientId: string, answer: RTCSessionDescriptionInit): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('webrtc_answer', { recipientId, answer });
    }
  }

  sendICECandidate(recipientId: string, candidate: RTCIceCandidateInit): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('webrtc_ice_candidate', { recipientId, candidate });
    }
  }

  // Utility methods
  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  // Update user status
  updateUserStatus(status: 'online' | 'offline' | 'busy'): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('update_status', { status });
    }
  }

  // Location sharing for activists
  shareLocation(location: { latitude: number; longitude: number }): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('share_location', { location, timestamp: new Date() });
    }
  }
}

export const socketService = new SocketService();
