// Core User Types
export type UserRole = 'youth' | 'activist' | 'admin';

export interface BaseUser {
  id: string;
  email: string;
  phone: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  role: UserRole;
  isVerified: boolean;
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface YouthUser extends BaseUser {
  role: 'youth';
  age: number;
  emergencyContacts: EmergencyContact[];
  medicalInfo?: MedicalInfo;
  preferences: YouthPreferences;
  parentalConsent?: boolean;
  schoolInfo?: SchoolInfo;
  guardianInfo?: GuardianInfo;
}

export interface ActivistUser extends BaseUser {
  role: 'activist';
  credentials: Credential[];
  specializations: string[];
  serviceArea: GeographicArea;
  availability: AvailabilitySchedule;
  responseTime: number;
  rating: number;
  totalResponses: number;
  successfulResponses: number;
  verificationStatus: 'pending' | 'verified' | 'rejected' | 'suspended';
  isOnline: boolean;
  lastLocationUpdate?: Date;
  bio?: string;
  languages: string[];
  certifications: Certification[];
}

export interface AdminUser extends BaseUser {
  role: 'admin';
  permissions: AdminPermission[];
  adminLevel: 'moderator' | 'super_admin';
  department?: string;
  accessLevel: number;
}

// Emergency SOS Types
export interface SOSRequest {
  id: string;
  userId: string;
  timestamp: Date;
  latitude: number;
  longitude: number;
  accuracy?: number;
  address?: string;
  emergencyType: 'medical' | 'safety' | 'mental_health' | 'general';
  status: 'active' | 'accepted' | 'resolved' | 'cancelled';
  assignedActivistId?: string;
  responseTime?: number;
  description?: string;
  audioRecording?: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  medicalInfo?: MedicalInfo;
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
  resolutionNotes?: string;
}

export interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  address?: string;
  timestamp: Date;
}

// Social Features Types
export interface Post {
  id: string;
  userId: string;
  content: string;
  mediaUrls?: string[];
  tags?: string[];
  category: 'general' | 'mental_health' | 'education' | 'social';
  privacy: 'public' | 'friends' | 'anonymous';
  reactions: Reaction[];
  comments: Comment[];
  reportCount: number;
  isModerated: boolean;
  moderatedBy?: string;
  moderatedAt?: Date;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Confession {
  id: string;
  userId?: string; // Optional for anonymous confessions
  content: string;
  isAnonymous: boolean;
  category: string;
  supportRequested: boolean;
  responses: ConfessionResponse[];
  mentalHealthFlags?: string[];
  reportCount: number;
  isModerated: boolean;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Supporting Types
export interface EmergencyContact {
  id: string;
  name: string;
  phone: string;
  relationship: string;
  isPrimary: boolean;
  isVerified: boolean;
  createdAt: Date;
}

export interface MedicalInfo {
  id?: string;
  bloodType?: string;
  allergies: string[];
  medications: string[];
  conditions: string[];
  emergencyNotes?: string;
  doctorName?: string;
  doctorPhone?: string;
  insuranceInfo?: string;
  isEncrypted: boolean;
  lastUpdated: Date;
}

export interface YouthPreferences {
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  theme: 'light' | 'dark' | 'auto';
  language: string;
  accessibility: AccessibilitySettings;
}

export interface Credential {
  id: string;
  type: string;
  issuer: string;
  number: string;
  expiryDate: Date;
  isVerified: boolean;
  verifiedBy?: string;
  verifiedAt?: Date;
  documentUrl?: string;
}

export interface Certification {
  id: string;
  name: string;
  issuer: string;
  issueDate: Date;
  expiryDate?: Date;
  credentialId: string;
  isActive: boolean;
  documentUrl?: string;
}

export interface GeographicArea {
  id?: string;
  city: string;
  state: string;
  country: string;
  radius: number; // in kilometers
  latitude: number;
  longitude: number;
  postalCode?: string;
}

export interface AvailabilitySchedule {
  id?: string;
  monday: TimeSlot[];
  tuesday: TimeSlot[];
  wednesday: TimeSlot[];
  thursday: TimeSlot[];
  friday: TimeSlot[];
  saturday: TimeSlot[];
  sunday: TimeSlot[];
  timezone: string;
  isActive: boolean;
}

export interface TimeSlot {
  id?: string;
  start: string; // HH:MM format
  end: string;   // HH:MM format
  isAvailable: boolean;
}

export interface AdminPermission {
  id: string;
  resource: string;
  actions: string[];
  scope?: string;
  grantedBy: string;
  grantedAt: Date;
}

export interface Reaction {
  id: string;
  userId: string;
  type: 'like' | 'love' | 'support' | 'care';
  createdAt: Date;
}

export interface Comment {
  id: string;
  userId: string;
  postId?: string;
  content: string;
  parentId?: string;
  replies?: Comment[];
  isDeleted: boolean;
  reportCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ConfessionResponse {
  id: string;
  confessionId: string;
  userId?: string;
  content: string;
  isFromCounselor: boolean;
  isAnonymous: boolean;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface NotificationSettings {
  sosAlerts: boolean;
  socialUpdates: boolean;
  eventReminders: boolean;
  emergencyContacts: boolean;
  pushEnabled: boolean;
  emailEnabled: boolean;
  smsEnabled: boolean;
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
  };
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'friends' | 'private';
  locationSharing: 'always' | 'emergency_only' | 'never';
  dataCollection: boolean;
  analyticsOptOut: boolean;
  marketingOptOut: boolean;
  thirdPartySharing: boolean;
  anonymousPosting: boolean;
  emergencyContactsVisible: boolean;
  medicalInfoVisible: boolean;
}

export interface AccessibilitySettings {
  fontSize: 'small' | 'medium' | 'large' | 'extra_large';
  highContrast: boolean;
  screenReader: boolean;
  voiceCommands: boolean;
  hapticFeedback: boolean;
}

// Additional Supporting Types
export interface SchoolInfo {
  id?: string;
  name: string;
  address: string;
  phone?: string;
  counselorName?: string;
  counselorPhone?: string;
  emergencyContact?: string;
  grade?: string;
}

export interface GuardianInfo {
  id?: string;
  name: string;
  relationship: string;
  phone: string;
  email?: string;
  address?: string;
  isEmergencyContact: boolean;
  hasLegalAuthority: boolean;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Authentication Types
export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  phone: string;
  role: UserRole;
  age?: number;
  parentalConsent?: boolean;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

export interface AuthState {
  user: BaseUser | YouthUser | ActivistUser | AdminUser | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Navigation Types
export type RootStackParamList = {
  // Auth Stack
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  ResetPassword: { token: string };

  // Main App
  YouthDashboard: undefined;
  ActivistDashboard: undefined;
  AdminDashboard: undefined;

  // SOS
  SOSTracking: { sosRequest: SOSRequest };
  SOSResponse: { sosId: string };
  SOSDetails: { sosRequest: SOSRequest };

  // Social
  SocialFeed: undefined;
  ConfessionsRoom: undefined;
  PostDetails: { postId: string };

  // Profile & Settings
  Profile: undefined;
  EditProfile: undefined;
  PrivacySettings: undefined;
  NotificationSettings: undefined;
  EmergencyContacts: undefined;
  MedicalInfo: undefined;

  // Admin
  UserManagement: undefined;
  ActivistVerifications: undefined;
  ContentModeration: undefined;
  SOSMonitoring: undefined;
  AdminAnalytics: undefined;
  SystemSettings: undefined;

  // Activist
  ActivistProfile: undefined;
  EventManagement: undefined;
  KnowledgeHub: undefined;
  Training: undefined;
  ActivistAnalytics: undefined;

  // Communication
  VideoCall: { recipientId: string; callType: 'audio' | 'video' };
  Chat: { recipientId: string };

  // Other
  ConsentHistory: undefined;
};

// WebRTC Types
export interface CallSession {
  id: string;
  participants: CallParticipant[];
  type: 'audio' | 'video';
  status: 'initiating' | 'ringing' | 'connected' | 'ended';
  startTime?: Date;
  endTime?: Date;
  duration?: number;
}

export interface CallParticipant {
  id: string;
  name: string;
  avatar?: string;
  role: UserRole;
  isLocal: boolean;
}

// Notification Types
export interface Notification {
  id: string;
  userId: string;
  type: 'sos_request' | 'sos_update' | 'message' | 'social' | 'system';
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  timestamp: Date;
  expiresAt?: Date;
}

// Event Types
export interface Event {
  id: string;
  organizerId: string;
  title: string;
  description: string;
  category: 'workshop' | 'support_group' | 'training' | 'social' | 'educational';
  startDate: Date;
  endDate: Date;
  location: {
    name: string;
    address: string;
    latitude?: number;
    longitude?: number;
  };
  isVirtual: boolean;
  virtualLink?: string;
  maxParticipants?: number;
  currentParticipants: number;
  registrationRequired: boolean;
  ageRestriction?: {
    min: number;
    max: number;
  };
  tags: string[];
  isPublic: boolean;
  status: 'draft' | 'published' | 'cancelled' | 'completed';
  createdAt: Date;
  updatedAt: Date;
}

// Analytics Types
export interface AnalyticsData {
  period: 'day' | 'week' | 'month' | 'year';
  startDate: Date;
  endDate: Date;
  metrics: {
    [key: string]: number | string;
  };
}

export interface UserAnalytics {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  usersByRole: {
    youth: number;
    activist: number;
    admin: number;
  };
  usersByAge: {
    [ageGroup: string]: number;
  };
  retentionRate: number;
}

export interface SOSAnalytics {
  totalRequests: number;
  activeRequests: number;
  resolvedRequests: number;
  averageResponseTime: number;
  requestsByType: {
    medical: number;
    safety: number;
    mental_health: number;
    general: number;
  };
  requestsByPriority: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  userId?: string;
  context?: string;
}

// File Upload Types
export interface FileUpload {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  uploadedBy: string;
  uploadedAt: Date;
  isPublic: boolean;
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
    [key: string]: any;
  };
}

// Search Types
export interface SearchFilters {
  query?: string;
  category?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  location?: {
    latitude: number;
    longitude: number;
    radius: number;
  };
  tags?: string[];
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface SearchResult<T> {
  items: T[];
  total: number;
  facets?: {
    [key: string]: {
      [value: string]: number;
    };
  };
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'tel' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'date' | 'file';
  required: boolean;
  placeholder?: string;
  options?: { label: string; value: string }[];
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
  };
}

export interface FormData {
  [key: string]: any;
}

export interface FormErrors {
  [key: string]: string;
}

// Theme Types
export interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  error: string;
  warning: string;
  success: string;
  info: string;
}

export interface Theme {
  colors: ThemeColors;
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  typography: {
    fontFamily: string;
    fontSize: {
      xs: number;
      sm: number;
      md: number;
      lg: number;
      xl: number;
    };
    fontWeight: {
      normal: string;
      medium: string;
      bold: string;
    };
  };
  borderRadius: {
    sm: number;
    md: number;
    lg: number;
  };
}
