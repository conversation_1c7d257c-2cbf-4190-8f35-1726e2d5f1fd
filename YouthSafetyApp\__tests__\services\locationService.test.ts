import { locationService } from '@/services/locationService';
import * as Location from 'expo-location';

// Mock expo-location
jest.mock('expo-location', () => ({
  requestForegroundPermissionsAsync: jest.fn(),
  requestBackgroundPermissionsAsync: jest.fn(),
  getCurrentPositionAsync: jest.fn(),
  watchPositionAsync: jest.fn(),
  reverseGeocodeAsync: jest.fn(),
  hasServicesEnabledAsync: jest.fn(),
  Accuracy: {
    BestForNavigation: 6,
    Highest: 5,
    High: 4,
    Balanced: 3,
    Low: 2,
    Lowest: 1,
  },
}));

describe('LocationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('requestPermissions', () => {
    it('should request foreground and background permissions', async () => {
      const mockForegroundPermission = { status: 'granted' };
      const mockBackgroundPermission = { status: 'granted' };

      (Location.requestForegroundPermissionsAsync as jest.Mock).mockResolvedValue(mockForegroundPermission);
      (Location.requestBackgroundPermissionsAsync as jest.Mock).mockResolvedValue(mockBackgroundPermission);

      const result = await locationService.requestPermissions();

      expect(Location.requestForegroundPermissionsAsync).toHaveBeenCalled();
      expect(Location.requestBackgroundPermissionsAsync).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should return false if foreground permission is denied', async () => {
      const mockForegroundPermission = { status: 'denied' };

      (Location.requestForegroundPermissionsAsync as jest.Mock).mockResolvedValue(mockForegroundPermission);

      const result = await locationService.requestPermissions();

      expect(result).toBe(false);
      expect(Location.requestBackgroundPermissionsAsync).not.toHaveBeenCalled();
    });
  });

  describe('getCurrentLocation', () => {
    it('should return current location with address', async () => {
      const mockPermission = { status: 'granted' };
      const mockLocation = {
        coords: {
          latitude: 37.7749,
          longitude: -122.4194,
          accuracy: 10,
        },
        timestamp: Date.now(),
      };
      const mockAddress = [
        {
          streetNumber: '123',
          street: 'Main St',
          city: 'San Francisco',
          region: 'CA',
          postalCode: '94102',
        },
      ];

      (Location.requestForegroundPermissionsAsync as jest.Mock).mockResolvedValue(mockPermission);
      (Location.requestBackgroundPermissionsAsync as jest.Mock).mockResolvedValue(mockPermission);
      (Location.getCurrentPositionAsync as jest.Mock).mockResolvedValue(mockLocation);
      (Location.reverseGeocodeAsync as jest.Mock).mockResolvedValue(mockAddress);

      const result = await locationService.getCurrentLocation();

      expect(result).toEqual({
        latitude: 37.7749,
        longitude: -122.4194,
        accuracy: 10,
        address: '123, Main St, San Francisco, CA, 94102',
        timestamp: expect.any(Date),
      });
    });

    it('should handle reverse geocoding failure gracefully', async () => {
      const mockPermission = { status: 'granted' };
      const mockLocation = {
        coords: {
          latitude: 37.7749,
          longitude: -122.4194,
          accuracy: 10,
        },
        timestamp: Date.now(),
      };

      (Location.requestForegroundPermissionsAsync as jest.Mock).mockResolvedValue(mockPermission);
      (Location.requestBackgroundPermissionsAsync as jest.Mock).mockResolvedValue(mockPermission);
      (Location.getCurrentPositionAsync as jest.Mock).mockResolvedValue(mockLocation);
      (Location.reverseGeocodeAsync as jest.Mock).mockRejectedValue(new Error('Geocoding failed'));

      const result = await locationService.getCurrentLocation();

      expect(result?.address).toBe('Unknown location');
    });
  });

  describe('calculateDistance', () => {
    it('should calculate distance between two points correctly', () => {
      // Distance between San Francisco and Los Angeles (approximately 559 km)
      const lat1 = 37.7749; // San Francisco
      const lon1 = -122.4194;
      const lat2 = 34.0522; // Los Angeles
      const lon2 = -118.2437;

      const distance = locationService.calculateDistance(lat1, lon1, lat2, lon2);

      // Allow for some margin of error in the calculation
      expect(distance).toBeCloseTo(559, 0);
    });

    it('should return 0 for same coordinates', () => {
      const distance = locationService.calculateDistance(37.7749, -122.4194, 37.7749, -122.4194);
      expect(distance).toBe(0);
    });
  });

  describe('startLocationTracking', () => {
    it('should start location tracking with callback', async () => {
      const mockPermission = { status: 'granted' };
      const mockSubscription = { remove: jest.fn() };
      const mockCallback = jest.fn();

      (Location.requestForegroundPermissionsAsync as jest.Mock).mockResolvedValue(mockPermission);
      (Location.requestBackgroundPermissionsAsync as jest.Mock).mockResolvedValue(mockPermission);
      (Location.watchPositionAsync as jest.Mock).mockResolvedValue(mockSubscription);

      await locationService.startLocationTracking(mockCallback);

      expect(Location.watchPositionAsync).toHaveBeenCalledWith(
        expect.objectContaining({
          accuracy: Location.Accuracy.BestForNavigation,
          timeInterval: 5000,
          distanceInterval: 10,
        }),
        expect.any(Function)
      );
    });
  });

  describe('stopLocationTracking', () => {
    it('should stop location tracking', async () => {
      const mockPermission = { status: 'granted' };
      const mockSubscription = { remove: jest.fn() };
      const mockCallback = jest.fn();

      (Location.requestForegroundPermissionsAsync as jest.Mock).mockResolvedValue(mockPermission);
      (Location.requestBackgroundPermissionsAsync as jest.Mock).mockResolvedValue(mockPermission);
      (Location.watchPositionAsync as jest.Mock).mockResolvedValue(mockSubscription);

      await locationService.startLocationTracking(mockCallback);
      await locationService.stopLocationTracking();

      expect(mockSubscription.remove).toHaveBeenCalled();
    });
  });

  describe('isLocationEnabled', () => {
    it('should return true when location services are enabled', async () => {
      (Location.hasServicesEnabledAsync as jest.Mock).mockResolvedValue(true);

      const result = await locationService.isLocationEnabled();

      expect(result).toBe(true);
      expect(Location.hasServicesEnabledAsync).toHaveBeenCalled();
    });

    it('should return false when location services are disabled', async () => {
      (Location.hasServicesEnabledAsync as jest.Mock).mockResolvedValue(false);

      const result = await locationService.isLocationEnabled();

      expect(result).toBe(false);
    });
  });

  describe('shareLocationWithEmergencyContacts', () => {
    it('should prepare location message for emergency contacts', async () => {
      const mockPermission = { status: 'granted' };
      const mockLocation = {
        coords: {
          latitude: 37.7749,
          longitude: -122.4194,
          accuracy: 10,
        },
        timestamp: Date.now(),
      };
      const mockAddress = [
        {
          streetNumber: '123',
          street: 'Main St',
          city: 'San Francisco',
          region: 'CA',
          postalCode: '94102',
        },
      ];

      (Location.requestForegroundPermissionsAsync as jest.Mock).mockResolvedValue(mockPermission);
      (Location.requestBackgroundPermissionsAsync as jest.Mock).mockResolvedValue(mockPermission);
      (Location.getCurrentPositionAsync as jest.Mock).mockResolvedValue(mockLocation);
      (Location.reverseGeocodeAsync as jest.Mock).mockResolvedValue(mockAddress);

      const emergencyContacts = [
        { name: 'John Doe', phone: '+1234567890' },
        { name: 'Jane Smith', phone: '+0987654321' },
      ];

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      await locationService.shareLocationWithEmergencyContacts(emergencyContacts, 'sos-123');

      expect(consoleSpy).toHaveBeenCalledWith(
        'Emergency location message:',
        expect.stringContaining('EMERGENCY:')
      );

      consoleSpy.mockRestore();
    });
  });
});
