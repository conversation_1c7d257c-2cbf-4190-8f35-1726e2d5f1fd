module.exports = {
  apps: [
    {
      name: 'youth-safety-api',
      script: 'dist/index.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 5000,
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 5000,
      },
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 5001,
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 5000,
      },
      // Logging
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process management
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Monitoring
      monitoring: false,
      
      // Advanced features
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'uploads'],
      
      // Environment variables
      env_file: '.env',
      
      // Graceful shutdown
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Health check
      health_check_grace_period: 3000,
      
      // Auto restart on file changes (development only)
      watch_options: {
        followSymlinks: false,
        usePolling: false,
      },
    },
    {
      name: 'youth-safety-worker',
      script: 'dist/workers/index.js',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        WORKER_TYPE: 'background',
      },
      env_production: {
        NODE_ENV: 'production',
        WORKER_TYPE: 'background',
      },
      env_staging: {
        NODE_ENV: 'staging',
        WORKER_TYPE: 'background',
      },
      
      // Logging
      log_file: './logs/worker-combined.log',
      out_file: './logs/worker-out.log',
      error_file: './logs/worker-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process management
      max_memory_restart: '512M',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Cron restart (restart every day at 2 AM)
      cron_restart: '0 2 * * *',
      
      // Auto restart
      autorestart: true,
    },
    {
      name: 'youth-safety-scheduler',
      script: 'dist/scheduler/index.js',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        SCHEDULER_TYPE: 'cron',
      },
      env_production: {
        NODE_ENV: 'production',
        SCHEDULER_TYPE: 'cron',
      },
      env_staging: {
        NODE_ENV: 'staging',
        SCHEDULER_TYPE: 'cron',
      },
      
      // Logging
      log_file: './logs/scheduler-combined.log',
      out_file: './logs/scheduler-out.log',
      error_file: './logs/scheduler-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process management
      max_memory_restart: '256M',
      restart_delay: 4000,
      max_restarts: 5,
      min_uptime: '30s',
      
      // Cron restart (restart every day at 1 AM)
      cron_restart: '0 1 * * *',
      
      // Auto restart
      autorestart: true,
    }
  ],
  
  deploy: {
    production: {
      user: 'deploy',
      host: ['production-server.com'],
      ref: 'origin/main',
      repo: '**************:username/youth-safety-app.git',
      path: '/var/www/youth-safety-backend',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    },
    
    staging: {
      user: 'deploy',
      host: ['staging-server.com'],
      ref: 'origin/develop',
      repo: '**************:username/youth-safety-app.git',
      path: '/var/www/youth-safety-backend-staging',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env staging',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    }
  }
};
