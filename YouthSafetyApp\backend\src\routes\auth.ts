import express from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import Joi from 'joi';
import { db } from '../database/connection';
import { logger } from '../utils/logger';
import { sendVerificationEmail, sendVerificationSMS } from '../services/notification';

const router = express.Router();

// Validation schemas
const registerSchema = Joi.object({
  email: Joi.string().email().required(),
  phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).required(),
  password: Joi.string().min(8).required(),
  firstName: Joi.string().min(2).max(50).required(),
  lastName: Joi.string().min(2).max(50).required(),
  role: Joi.string().valid('youth', 'activist').required(),
  age: Joi.when('role', {
    is: 'youth',
    then: Joi.number().min(13).max(25).required(),
    otherwise: Joi.forbidden()
  }),
  parentalConsent: Joi.when('age', {
    is: Joi.number().less(18),
    then: Joi.boolean().valid(true).required(),
    otherwise: Joi.boolean()
  })
});

const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().required()
});

// Register endpoint
router.post('/register', async (req, res) => {
  try {
    const { error, value } = registerSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const { email, phone, password, firstName, lastName, role, age, parentalConsent } = value;

    // Check if user already exists
    const existingUser = await db('users')
      .where({ email })
      .orWhere({ phone })
      .first();

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this email or phone already exists'
      });
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Start transaction
    const trx = await db.transaction();

    try {
      // Create user
      const [user] = await trx('users')
        .insert({
          email,
          phone,
          password_hash: passwordHash,
          first_name: firstName,
          last_name: lastName,
          role
        })
        .returning(['id', 'email', 'first_name', 'last_name', 'role']);

      // Create role-specific profile
      if (role === 'youth') {
        await trx('youth_profiles').insert({
          user_id: user.id,
          age,
          parental_consent: parentalConsent || false
        });
      } else if (role === 'activist') {
        await trx('activist_profiles').insert({
          user_id: user.id,
          verification_status: 'pending'
        });
      }

      await trx.commit();

      // Generate JWT token
      const token = jwt.sign(
        { userId: user.id, role: user.role },
        process.env.JWT_SECRET!,
        { expiresIn: '7d' }
      );

      // Send verification emails/SMS (async)
      sendVerificationEmail(user.email, user.first_name).catch(err => 
        logger.error('Failed to send verification email:', err)
      );
      sendVerificationSMS(phone).catch(err => 
        logger.error('Failed to send verification SMS:', err)
      );

      res.status(201).json({
        success: true,
        message: 'User registered successfully',
        data: {
          user: {
            id: user.id,
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name,
            role: user.role
          },
          token
        }
      });

    } catch (error) {
      await trx.rollback();
      throw error;
    }

  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Login endpoint
router.post('/login', async (req, res) => {
  try {
    const { error, value } = loginSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const { email, password } = value;

    // Find user
    const user = await db('users')
      .select('id', 'email', 'password_hash', 'first_name', 'last_name', 'role', 'is_verified', 'is_active')
      .where({ email })
      .first();

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated'
      });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Update last login
    await db('users')
      .where({ id: user.id })
      .update({ last_login_at: new Date() });

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, role: user.role },
      process.env.JWT_SECRET!,
      { expiresIn: '7d' }
    );

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          role: user.role,
          isVerified: user.is_verified
        },
        token
      }
    });

  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Verify email endpoint
router.post('/verify-email', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Verification token is required'
      });
    }

    // Verify token and update user
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
    
    await db('users')
      .where({ id: decoded.userId })
      .update({ 
        is_verified: true,
        email_verified_at: new Date()
      });

    res.json({
      success: true,
      message: 'Email verified successfully'
    });

  } catch (error) {
    logger.error('Email verification error:', error);
    res.status(400).json({
      success: false,
      message: 'Invalid or expired verification token'
    });
  }
});

export default router;
