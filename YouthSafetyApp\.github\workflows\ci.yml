name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run linter
      run: npm run lint
      
    - name: Run type check
      run: npm run type-check
      
    - name: Run tests
      run: npm run test:coverage
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

  backend-test:
    name: Backend Test
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: youth_safety_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json
        
    - name: Install backend dependencies
      working-directory: ./backend
      run: npm ci
      
    - name: Run backend linter
      working-directory: ./backend
      run: npm run lint
      
    - name: Run backend type check
      working-directory: ./backend
      run: npm run type-check
      
    - name: Run database migrations
      working-directory: ./backend
      run: npm run migrate:latest
      env:
        NODE_ENV: test
        DB_HOST: localhost
        DB_PORT: 5432
        DB_USER: postgres
        DB_PASSWORD: postgres
        DB_NAME: youth_safety_test
        
    - name: Run backend tests
      working-directory: ./backend
      run: npm run test:coverage
      env:
        NODE_ENV: test
        DB_HOST: localhost
        DB_PORT: 5432
        DB_USER: postgres
        DB_PASSWORD: postgres
        DB_NAME: youth_safety_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret-key

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'
        
    - name: Run npm audit
      run: npm audit --audit-level moderate
      
    - name: Run backend npm audit
      working-directory: ./backend
      run: npm audit --audit-level moderate

  build-android:
    name: Build Android
    runs-on: ubuntu-latest
    needs: [test, backend-test]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: '17'
        
    - name: Setup Android SDK
      uses: android-actions/setup-android@v3
      
    - name: Install dependencies
      run: npm ci
      
    - name: Setup Expo
      uses: expo/expo-github-action@v8
      with:
        expo-version: latest
        token: ${{ secrets.EXPO_TOKEN }}
        
    - name: Build Android APK
      run: |
        if [ "${{ github.ref }}" = "refs/heads/main" ]; then
          expo build:android --type apk --release-channel production
        else
          expo build:android --type apk --release-channel staging
        fi
        
    - name: Upload Android artifact
      uses: actions/upload-artifact@v4
      with:
        name: android-apk
        path: '*.apk'

  build-ios:
    name: Build iOS
    runs-on: macos-latest
    needs: [test, backend-test]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Setup Expo
      uses: expo/expo-github-action@v8
      with:
        expo-version: latest
        token: ${{ secrets.EXPO_TOKEN }}
        
    - name: Build iOS IPA
      run: |
        if [ "${{ github.ref }}" = "refs/heads/main" ]; then
          expo build:ios --type archive --release-channel production
        else
          expo build:ios --type archive --release-channel staging
        fi
        
    - name: Upload iOS artifact
      uses: actions/upload-artifact@v4
      with:
        name: ios-ipa
        path: '*.ipa'

  deploy-backend:
    name: Deploy Backend
    runs-on: ubuntu-latest
    needs: [backend-test, security-scan]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json
        
    - name: Install backend dependencies
      working-directory: ./backend
      run: npm ci --only=production
      
    - name: Build backend
      working-directory: ./backend
      run: npm run build
      
    - name: Deploy to production
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USER }}
        key: ${{ secrets.PRODUCTION_SSH_KEY }}
        script: |
          cd /var/www/youth-safety-backend
          git pull origin main
          npm ci --only=production
          npm run build
          npm run migrate:latest
          pm2 restart youth-safety-api

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [backend-test, security-scan]
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to staging
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.STAGING_HOST }}
        username: ${{ secrets.STAGING_USER }}
        key: ${{ secrets.STAGING_SSH_KEY }}
        script: |
          cd /var/www/youth-safety-backend-staging
          git pull origin develop
          npm ci --only=production
          npm run build
          npm run migrate:latest
          pm2 restart youth-safety-api-staging

  notify:
    name: Notify
    runs-on: ubuntu-latest
    needs: [deploy-backend, build-android, build-ios]
    if: always() && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    
    steps:
    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
